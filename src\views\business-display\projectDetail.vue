<template>
    <div class="project-detail-containers">
      <div class="content relative">
        <div
          class="absolute w-1/2 text-center text-[42px] font-family-youshebiaotihei tracking-[8px] leading-[90px] title z-10 top-0 left-2/4 transform -translate-x-1/2" style="z-index: 31; text-shadow: 0 2px 4px rgba(0,0,0,0.7);">
          {{ title }}
        </div>
        <div
          class="absolute text-center text-[30px] font-family-youshebiaotihei tracking-[8px] title z-10 top-[120px] left-2/4 transform -translate-x-1/2" style="text-shadow: 0 2px 26px rgba(0,0,0,0.7);">
          {{ projectData.项目名称 }}</div>
        <div class="bg"></div>
        <div class="leftBg"></div>
        <div class="bottomBg"></div>
        
        <!-- 左侧按钮 -->
        <div class="btns">
          <div v-for="(item, index) in filteredBtns" 
            :class="[
              btnsActive == index && 'active', 
              (isInternationalProject && index > 0 || isDesignProject && index > 0) && 'disabled'
            ]" 
            :key="index"
            @click="handleBtnClick(index)">
            <span> {{ item.name }}</span>
          </div>
        </div>
        
        <!-- 主要内容区域 -->
        <div class="main-content">
          <!-- 左边内容展示区域 (4/10) -->
          <div class="left-content-container">
            <item1 :projectData="projectData" :isInternationalProject="isInternationalProject" :personData="personData" v-if="btnsActive == 0" @show-personnel-modal="showPersonnelModal" />
            <item2 :projectData="projectData" v-if="btnsActive == 1" />
            <item3 :projectData="projectData" v-if="btnsActive == 2" />
          </div>
          
          <!-- 右边图片展示区域 (6/10) -->
          <div class="right-image-section">
              <!-- 右边主要图片展示区域 -->
    <div class="right-image-container">
      <div class="image-display-area">
        <img v-if="rightImageSrc && currentMediaType === 'image'" :src="rightImageSrc" alt="右侧展示图片" />
        <div v-else-if="!rightImageSrc && !rightVideoSrc && mediaItems.length === 0" class="no-image-placeholder">
          <span>暂无内容</span>
        </div>
      </div>
    </div>
            
            <!-- 最右边缩略图容器 -->
            <div v-if="!isUsingDefaultImage && (images.length > 0 || videos.length > 0)" class="thumbnails-container">
              <div v-for="(mediaItem, index) in mediaItems" :key="index" class="thumbnail"
                :class="{ 'active': currentMediaIndex === index }">
                <!-- 图片缩略图 -->
                <img v-if="mediaItem.type === 'image'" :src="mediaItem.thumbnail" 
                  :alt="t('projectDetail.actions.thumbnailAlt')"
                  @click="() => handleThumbnailClick(index, mediaItem)" />
                
                <!-- 视频缩略图 -->
                <div v-else-if="mediaItem.type === 'video'" class="video-thumbnail-container"
                  @click="() => handleThumbnailClick(index, mediaItem)">
                  <video :src="mediaItem.thumbnail" 
                    class="video-thumbnail" 
                    muted 
                    preload="metadata"
                    @loadedmetadata="($event) => {
                      $event.target.currentTime = 2; // 设置为2秒处的帧作为缩略图
                    }">
                  </video>
                  <div class="video-play-icon">
                    <svg viewBox="0 0 24 24" width="24" height="24">
                      <path fill="currentColor" d="M8,5.14V19.14L19,12.14L8,5.14Z" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 删除悬浮覆盖层和按钮，因为内容现在直接显示在左边 -->
        
        <!-- 返回首页按钮 -->
        <div class="backHome-button" @click="backHome">
          <img src="@/assets/images/header/back.png" alt="" />
          <span>返回首页</span>
        </div>
        <!-- 返回上一级按钮 -->
        <div class="back-button" @click="back">
          <span>返回上一级</span>
        </div>
      </div>
    </div>
    
    <!-- 人员列表弹窗 -->
    <div v-if="showModal" class="personnel-modal-overlay" @click="closeModal">
      <div class="personnel-modal" @click.stop>
        <div class="modal-background">
          <img src="@/assets/images/project/popup.png" alt="" class="modal-bg-image" />
        </div>
        <div class="modal-content">
          <div class="modal-header">
            <h3 class="modal-title">项目人员列表</h3>
            <button class="close-btn" @click="closeModal">×</button>
          </div>
          <div class="modal-body">
            <div v-if="modalLoading" class="loading">
              <div class="loading-spinner"></div>
              <p>加载中...</p>
            </div>
            <div v-else class="personnel-table-container">
              <table class="personnel-table">
                <thead>
                  <tr>
                    <th>序号</th>
                    <th>姓名</th>
                    <th>性别</th>
                    <th>手机号</th>
                    <th>身份证号</th>
                    <th>参建单位</th>
                    <th>班组</th>
                    <th>工种类型</th>
                    <th>岗位</th>
                    <th>进场日期</th>
                    <th>现场照片</th>
                    <th>二维码</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr 
                    v-for="(person, index) in personnelData" 
                    :key="person.personId || index"
                  >
                    <td>{{ (pagination.pageNo - 1) * pagination.pageSize + index + 1 }}</td>
                    <td>{{ person.personName || '-' }}</td>
                    <td>{{ person.sex === 1 ? '男' : person.sex === 0 ? '女' : '-' }}</td>
                    <td>{{ person.phone || '-' }}</td>
                    <td>{{ maskIdCard(person.identityCardNo) }}</td>
                    <td>{{ person.buildUnitName || '-' }}</td>
                    <td>{{ person.teamsName || '-' }}</td>
                    <td>{{ person.coworkerTypeName || '-' }}</td>
                    <td>{{ person.personPost || person.postJobTypeName || '-' }}</td>
                    <td>{{ formatDate(person.entranceDate) }}</td>
                    <td>
                      <img 
                        v-if="person.personPic" 
                        :src="person.personPic" 
                        alt="现场照片" 
                        class="photo-thumbnail"
                        @click="viewPhoto(person.personPic)"
                      />
                      <span v-else class="no-photo">-</span>
                    </td>
                    <td>
                      <img 
                        v-if="person.qrcode" 
                        :src="person.qrcode" 
                        alt="二维码" 
                        class="qr-code"
                        @click="viewQRCode(person.qrcode)"
                      />
                      <span v-else class="no-qr">-</span>
                    </td>
                    <td>
                      <button 
                        class="action-btn view-btn" 
                        @click="viewPersonDetail(person)"
                      >
                        查看详情
                      </button>
                    </td>
                  </tr>
                  <tr v-if="personnelData.length === 0 && !modalLoading">
                    <td colspan="13" class="no-data">暂无人员数据</td>
                  </tr>
                </tbody>
              </table>
              
              <!-- 分页组件 -->
              <div v-if="pagination.total > 0" class="pagination-container">
                <div class="pagination-info">
                  共 {{ pagination.total }} 条数据，第 {{ pagination.pageNo }} / {{ Math.ceil(pagination.total / pagination.pageSize) }} 页
                </div>
                <div class="pagination-controls">
                  <button 
                    class="page-btn" 
                    :disabled="pagination.pageNo === 1"
                    @click="changePage(1)"
                  >
                    首页
                  </button>
                  <button 
                    class="page-btn" 
                    :disabled="pagination.pageNo === 1"
                    @click="changePage(pagination.pageNo - 1)"
                  >
                    上一页
                  </button>
                  <span class="page-numbers">
                    <button
                      v-for="page in getPageNumbers()"
                      :key="page"
                      class="page-number"
                      :class="{ active: page === pagination.pageNo }"
                      @click="changePage(page)"
                    >
                      {{ page }}
                    </button>
                  </span>
                  <button 
                    class="page-btn" 
                    :disabled="pagination.pageNo >= Math.ceil(pagination.total / pagination.pageSize)"
                    @click="changePage(pagination.pageNo + 1)"
                  >
                    下一页
                  </button>
                  <button 
                    class="page-btn" 
                    :disabled="pagination.pageNo >= Math.ceil(pagination.total / pagination.pageSize)"
                    @click="changePage(Math.ceil(pagination.total / pagination.pageSize))"
                  >
                    末页
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 人员详情列表弹窗 -->
    <div v-if="showPersonnelDetailModal" class="personnel-detail-modal-overlay" @click="closePersonnelDetailModal">
      <div class="personnel-detail-modal" @click.stop>
        <div class="modal-background">
          <img src="@/assets/images/project/popup.png" alt="" class="modal-bg-image" />
        </div>
        <div class="modal-content">
          <div class="modal-header">
            <!-- 左上角返回按钮 -->
            <div class="back-to-list-button" @click="backToPersonnelList">
              <img src="@/assets/images/header/back.png" alt="" />
              <span>返回</span>
            </div>
            <h3 class="modal-title">人员详情列表</h3>
            <button class="close-btn" @click.stop="closePersonnelDetailModal">×</button>
          </div>
          <div class="modal-body">
            <div v-if="currentPersonDetail" class="person-detail-info">
              <!-- 8个标签页导航 -->
              <div class="personnel-tabs-navigation">
                <div 
                  v-for="(tab, index) in personnelTabs" 
                  :key="tab.id"
                  :class="['tab-item', { 'active': activePersonnelTab === index }]"
                  @click="switchPersonnelTab(index)"
                >
                  <div class="tab-block">
                    <span class="tab-name">{{ tab.name }}</span>
                  </div>
                </div>
              </div>

              <!-- 标签页内容区域 -->
              <div class="personnel-tabs-content">
                <!-- 1. 基本信息 -->
                <div v-if="activePersonnelTab === 0" class="tab-content basic-info">
                  <!-- <h4 class="section-title">基本信息</h4> -->
                  <div class="info-display-grid">
                    <div class="info-item">
                      <img src="@/assets/images/project/basicInfo.png" alt="基本信息图标" class="info-icon" />
                      <div class="info-text">
                        <div class="label">姓名:</div>
                        <div class="value">{{ currentPersonDetail.personName || '-' }}</div>
                      </div>
                    </div>
                    <div class="info-item">
                      <img src="@/assets/images/project/basicInfo.png" alt="基本信息图标" class="info-icon" />
                      <div class="info-text">
                        <div class="label">出生日期:</div>
                        <div class="value">{{ formatDate(currentPersonDetail.birthdayDate) }}</div>
                      </div>
                    </div>
                    <div class="info-item">
                      <img src="@/assets/images/project/basicInfo.png" alt="基本信息图标" class="info-icon" />
                      <div class="info-text">
                        <div class="label">性别:</div>
                        <div class="value">{{ currentPersonDetail.sex === 1 ? '男' : currentPersonDetail.sex === 0 ? '女' : '-' }}</div>
                      </div>
                    </div>
                    <div class="info-item">
                      <img src="@/assets/images/project/basicInfo.png" alt="基本信息图标" class="info-icon" />
                      <div class="info-text">
                        <div class="label">证件号码:</div>
                        <div class="value">{{ maskIdCard(currentPersonDetail.identityCardNo) }}</div>
                      </div>
                    </div>
                    <div class="info-item">
                      <img src="@/assets/images/project/basicInfo.png" alt="基本信息图标" class="info-icon" />
                      <div class="info-text">
                        <div class="label">民族:</div>
                        <div class="value">{{ currentPersonDetail.ethnicGroupsTypeName || '-' }}</div>
                      </div>
                    </div>
                    <div class="info-item">
                      <img src="@/assets/images/project/basicInfo.png" alt="基本信息图标" class="info-icon" />
                      <div class="info-text">
                        <div class="label">签发机关:</div>
                        <div class="value">{{ currentPersonDetail.issuingAuthority || '-' }}</div>
                      </div>
                    </div>
                    <div class="info-item">
                      <img src="@/assets/images/project/basicInfo.png" alt="基本信息图标" class="info-icon" />
                      <div class="info-text">
                        <div class="label">证件开始期限:</div>
                        <div class="value">{{ formatDate(currentPersonDetail.identityStartDate) }}</div>
                      </div>
                    </div>
                    <div class="info-item">
                      <img src="@/assets/images/project/basicInfo.png" alt="基本信息图标" class="info-icon" />
                      <div class="info-text">
                        <div class="label">证件结束期限:</div>
                        <div class="value">{{ formatDate(currentPersonDetail.identityEndDate) }}</div>
                      </div>
                    </div>
                    <div class="info-item">
                      <img src="@/assets/images/project/basicInfo.png" alt="基本信息图标" class="info-icon" />
                      <div class="info-text">
                        <div class="label">人员地址:</div>
                        <div class="value">{{ getPersonAddress(currentPersonDetail) }}</div>
                      </div>
                    </div>
                    <div class="info-item span-2">
                      <img src="@/assets/images/project/basicInfo.png" alt="基本信息图标" class="info-icon" />
                      <div class="info-text">
                        <div class="label">详细地址:</div>
                        <div class="value">{{ currentPersonDetail.homeAddress || '-' }}</div>
                      </div>
                    </div>
                    
                    <!-- 身份证照片 - 与姓名对齐 -->
                    <div class="info-item photo-item-inline">
                      <img src="@/assets/images/project/basicInfo.png" alt="基本信息图标" class="info-icon" />
                      <div class="info-text">
                        <div class="label">身份证照片:</div>
                        <div class="value photo-value">
                          <img v-if="currentPersonDetail.identityPic" :src="currentPersonDetail.identityPic" alt="身份证照片" class="info-detail-photo" />
                          <span v-else class="no-photo-text">暂无身份证照片</span>
                        </div>
                      </div>
                    </div>
                    <!-- 现场照片 - 与出生日期对齐 -->
                    <div class="info-item photo-item-inline">
                      <img src="@/assets/images/project/basicInfo.png" alt="基本信息图标" class="info-icon" />
                      <div class="info-text">
                        <div class="label">现场照片:</div>
                        <div class="value photo-value">
                          <img v-if="currentPersonDetail.personPic" :src="currentPersonDetail.personPic" alt="现场照片" class="info-detail-photo" />
                          <span v-else class="no-photo-text">暂无现场照片</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 2. 岗位信息 -->
                <div v-if="activePersonnelTab === 1" class="tab-content position-info">
                  <!-- <h4 class="section-title">岗位信息</h4> -->
                  <div class="info-display-grid">
                    <div class="info-item">
                      <img src="@/assets/images/project/basicInfo.png" alt="基本信息图标" class="info-icon" />
                      <div class="info-text">
                        <div class="label">进场时间:</div>
                        <div class="value">{{ formatDate(currentPersonDetail.entranceDate) }}</div>
                      </div>
                    </div>
                    <div class="info-item">
                      <img src="@/assets/images/project/basicInfo.png" alt="基本信息图标" class="info-icon" />
                      <div class="info-text">
                        <div class="label">退场时间:</div>
                        <div class="value">{{ formatDate(currentPersonDetail.exitDate) }}</div>
                      </div>
                    </div>
                    <div class="info-item">
                      <img src="@/assets/images/project/basicInfo.png" alt="基本信息图标" class="info-icon" />
                      <div class="info-text">
                        <div class="label">企业单位:</div>
                        <div class="value">{{ currentPersonDetail.buildUnitName || '-' }}</div>
                      </div>
                    </div>
                    <div class="info-item">
                      <img src="@/assets/images/project/basicInfo.png" alt="基本信息图标" class="info-icon" />
                      <div class="info-text">
                        <div class="label">人员类型:</div>
                        <div class="value">{{ currentPersonDetail.personTypeName || '-' }}</div>
                      </div>
                    </div>
                    <div class="info-item">
                      <img src="@/assets/images/project/basicInfo.png" alt="基本信息图标" class="info-icon" />
                      <div class="info-text">
                        <div class="label">班组:</div>
                        <div class="value">{{ currentPersonDetail.teamsName || '-' }}</div>
                      </div>
                    </div>
                    <div class="info-item">
                      <img src="@/assets/images/project/basicInfo.png" alt="基本信息图标" class="info-icon" />
                      <div class="info-text">
                        <div class="label">工种:</div>
                        <div class="value">{{ currentPersonDetail.coworkerTypeName || '-' }}</div>
                      </div>
                    </div>
                    <div class="info-item">
                      <img src="@/assets/images/project/basicInfo.png" alt="基本信息图标" class="info-icon" />
                      <div class="info-text">
                        <div class="label">手机号:</div>
                        <div class="value">{{ currentPersonDetail.phone || '-' }}</div>
                      </div>
                    </div>
                    <div class="info-item">
                      <img src="@/assets/images/project/basicInfo.png" alt="基本信息图标" class="info-icon" />
                      <div class="info-text">
                        <div class="label">岗位:</div>
                        <div class="value">{{ currentPersonDetail.personPost || currentPersonDetail.postJobTypeName || '-' }}</div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 3. 资格证书 -->
                <div v-if="activePersonnelTab === 2" class="tab-content certificate-info">
                  <!-- <h4 class="section-title">资格证书</h4> -->
                  <div v-if="certificateLoading" class="loading">
                    <div class="loading-spinner"></div>
                    <p>加载中...</p>
                  </div>
                  <div v-else>
                    <div v-if="certificateData.length === 0" class="no-data">暂无证书信息</div>
                    <div v-else>
                      <div v-for="(cert, index) in certificateData" :key="index" class="certificate-section">
                        <div class="info-display-grid">
                          <div class="info-item">
                            <img src="@/assets/images/project/qualificationCertificate.png" alt="资格证书图标" class="info-icon" />
                            <div class="info-text">
                              <div class="label">证书类别:</div>
                              <div class="value">{{ cert.certificateType || '-' }}</div>
                            </div>
                          </div>
                          <div class="info-item">
                            <img src="@/assets/images/project/qualificationCertificate.png" alt="资格证书图标" class="info-icon" />
                            <div class="info-text">
                              <div class="label">证书编号:</div>
                              <div class="value">{{ cert.certificateNumber || '-' }}</div>
                            </div>
                          </div>
                          <div class="info-item">
                            <img src="@/assets/images/project/qualificationCertificate.png" alt="资格证书图标" class="info-icon" />
                            <div class="info-text">
                              <div class="label">发证日期:</div>
                              <div class="value">{{ formatDate(cert.issueDate) }}</div>
                            </div>
                          </div>
                          <div class="info-item">
                            <img src="@/assets/images/project/qualificationCertificate.png" alt="资格证书图标" class="info-icon" />
                            <div class="info-text">
                              <div class="label">截止日期:</div>
                              <div class="value">{{ formatDate(cert.deadlineDate) }}</div>
                            </div>
                          </div>
                          <div class="info-item">
                            <img src="@/assets/images/project/qualificationCertificate.png" alt="资格证书图标" class="info-icon" />
                            <div class="info-text">
                              <div class="label">颁发机构:</div>
                              <div class="value">{{ cert.issueAuthority || '-' }}</div>
                            </div>
                          </div>
                          <div class="info-item span-2">
                            <img src="@/assets/images/project/qualificationCertificate.png" alt="资格证书图标" class="info-icon" />
                            <div class="info-text">
                              <div class="label">证书文件:</div>
                              <div class="value">
                                <div class="attachment-list">
                                  <img v-if="cert.certificateFile1" :src="cert.certificateFile1" alt="证书文件1" class="cert-attachment" />
                                  <img v-if="cert.certificateFile2" :src="cert.certificateFile2" alt="证书文件2" class="cert-attachment" />
                                  <img v-if="cert.certificateFile3" :src="cert.certificateFile3" alt="证书文件3" class="cert-attachment" />
                                  <span v-if="!cert.certificateFile1 && !cert.certificateFile2 && !cert.certificateFile3" class="no-attachment">暂无文件</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div v-if="index < certificateData.length - 1" class="certificate-divider"></div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 4. 评价信息 -->
                <div v-if="activePersonnelTab === 3" class="tab-content evaluation-info">
                  <!-- <h4 class="section-title">评价信息</h4> -->
                  <div v-if="evaluationLoading" class="loading">
                    <div class="loading-spinner"></div>
                    <p>加载中...</p>
                  </div>
                  <div v-else>
                    <div class="data-table-container">
                      <table class="data-table">
                        <thead>
                          <tr>
                            <th>序号</th>
                            <th>时间</th>
                            <th>奖惩类型</th>
                            <th>奖惩描述</th>
                            <th>图片</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(item, index) in evaluationData" :key="index">
                            <td>{{ (evaluationPagination.pageNo - 1) * evaluationPagination.pageSize + index + 1 }}</td>
                            <td>{{ formatDate(item.evaluateDate) }}</td>
                            <td>
                              <span :class="['status-badge', item.type === '奖励' ? 'status-reward' : 'status-penalty']">
                                {{ item.type || '-' }}
                              </span>
                            </td>
                            <td>{{ item.memo || '-' }}</td>
                            <td>
                              <div class="evaluation-pics">
                                <img v-if="item.evaluatePic1" :src="item.evaluatePic1" alt="图片1" class="evaluation-pic" />
                                <img v-if="item.evaluatePic2" :src="item.evaluatePic2" alt="图片2" class="evaluation-pic" />
                                <img v-if="item.evaluatePic3" :src="item.evaluatePic3" alt="图片3" class="evaluation-pic" />
                                <span v-if="!item.evaluatePic1 && !item.evaluatePic2 && !item.evaluatePic3" class="no-pic">-</span>
                              </div>
                            </td>
                          </tr>
                          <tr v-if="evaluationData.length === 0">
                            <td colspan="5" class="no-data">暂无评价信息</td>
                          </tr>
                        </tbody>
                      </table>
                      <!-- 评价信息分页 -->
                      <div v-if="evaluationPagination.total > 0" class="pagination-container">
                        <div class="pagination-info">
                          共 {{ evaluationPagination.total }} 条数据，第 {{ evaluationPagination.pageNo }} / {{ Math.ceil(evaluationPagination.total / evaluationPagination.pageSize) }} 页
                        </div>
                        <div class="pagination-controls">
                          <button class="page-btn" :disabled="evaluationPagination.pageNo === 1" @click="changeEvaluationPage(1)">首页</button>
                          <button class="page-btn" :disabled="evaluationPagination.pageNo === 1" @click="changeEvaluationPage(evaluationPagination.pageNo - 1)">上一页</button>
                          <button class="page-btn" :disabled="evaluationPagination.pageNo >= Math.ceil(evaluationPagination.total / evaluationPagination.pageSize)" @click="changeEvaluationPage(evaluationPagination.pageNo + 1)">下一页</button>
                          <button class="page-btn" :disabled="evaluationPagination.pageNo >= Math.ceil(evaluationPagination.total / evaluationPagination.pageSize)" @click="changeEvaluationPage(Math.ceil(evaluationPagination.total / evaluationPagination.pageSize))">末页</button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 5. 安全教育 -->
                <div v-if="activePersonnelTab === 4" class="tab-content safety-education">
                  <!-- <h4 class="section-title">安全教育</h4> -->
                  <div v-if="safetyEducationLoading" class="loading">
                    <div class="loading-spinner"></div>
                    <p>加载中...</p>
                  </div>
                  <div v-else>
                    <div class="data-table-container">
                      <table class="data-table">
                        <thead>
                          <tr>
                            <th>序号</th>
                            <th>培训时间</th>
                            <th>培训类型</th>
                            <th>培训主题</th>
                            <th>附件</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(item, index) in safetyEducationData" :key="index">
                            <td>{{ (safetyEducationPagination.pageNo - 1) * safetyEducationPagination.pageSize + index + 1 }}</td>
                            <td>{{ formatDate(item.trainingDate) }}</td>
                            <td>{{ item.trainingTypeName || '-' }}</td>
                            <td>{{ item.trainingTitle || '-' }}</td>
                            <td>
                              <div v-if="item.trainingFile" class="attachment-link">
                                <a :href="item.trainingFile" target="_blank">查看附件</a>
                              </div>
                              <span v-else class="no-attachment">-</span>
                            </td>
                          </tr>
                          <tr v-if="safetyEducationData.length === 0">
                            <td colspan="5" class="no-data">暂无安全教育记录</td>
                          </tr>
                        </tbody>
                      </table>
                      <!-- 安全教育分页 -->
                      <div v-if="safetyEducationPagination.total > 0" class="pagination-container">
                        <div class="pagination-info">
                          共 {{ safetyEducationPagination.total }} 条数据，第 {{ safetyEducationPagination.pageNo }} / {{ Math.ceil(safetyEducationPagination.total / safetyEducationPagination.pageSize) }} 页
                        </div>
                        <div class="pagination-controls">
                          <button class="page-btn" :disabled="safetyEducationPagination.pageNo === 1" @click="changeSafetyEducationPage(1)">首页</button>
                          <button class="page-btn" :disabled="safetyEducationPagination.pageNo === 1" @click="changeSafetyEducationPage(safetyEducationPagination.pageNo - 1)">上一页</button>
                          <button class="page-btn" :disabled="safetyEducationPagination.pageNo >= Math.ceil(safetyEducationPagination.total / safetyEducationPagination.pageSize)" @click="changeSafetyEducationPage(safetyEducationPagination.pageNo + 1)">下一页</button>
                          <button class="page-btn" :disabled="safetyEducationPagination.pageNo >= Math.ceil(safetyEducationPagination.total / safetyEducationPagination.pageSize)" @click="changeSafetyEducationPage(Math.ceil(safetyEducationPagination.total / safetyEducationPagination.pageSize))">末页</button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 6. 不良行为 -->
                <div v-if="activePersonnelTab === 5" class="tab-content bad-behavior">
                  <!-- <h4 class="section-title">不良行为</h4> -->
                  <div v-if="badBehaviorLoading" class="loading">
                    <div class="loading-spinner"></div>
                    <p>加载中...</p>
                  </div>
                  <div v-else>
                    <div class="data-table-container">
                      <table class="data-table">
                        <thead>
                          <tr>
                            <th>序号</th>
                            <th>时间</th>
                            <th>不良行为内容</th>
                            <th>行为结果</th>
                            <th>附件</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(item, index) in badBehaviorData" :key="index">
                            <td>{{ (badBehaviorPagination.pageNo - 1) * badBehaviorPagination.pageSize + index + 1 }}</td>
                            <td>{{ formatDate(item.behaviorDate) }}</td>
                            <td>{{ item.behaviorContent || '-' }}</td>
                            <td>{{ item.behaviorResult || '-' }}</td>
                            <td>
                              <div v-if="item.behaviorFileUrl" class="attachment-link">
                                <a :href="item.behaviorFileUrl" target="_blank">查看附件</a>
                              </div>
                              <span v-else class="no-attachment">-</span>
                            </td>
                          </tr>
                          <tr v-if="badBehaviorData.length === 0">
                            <td colspan="5" class="no-data">暂无不良行为记录</td>
                          </tr>
                        </tbody>
                      </table>
                      <!-- 不良行为分页 -->
                      <div v-if="badBehaviorPagination.total > 0" class="pagination-container">
                        <div class="pagination-info">
                          共 {{ badBehaviorPagination.total }} 条数据，第 {{ badBehaviorPagination.pageNo }} / {{ Math.ceil(badBehaviorPagination.total / badBehaviorPagination.pageSize) }} 页
                        </div>
                        <div class="pagination-controls">
                          <button class="page-btn" :disabled="badBehaviorPagination.pageNo === 1" @click="changeBadBehaviorPage(1)">首页</button>
                          <button class="page-btn" :disabled="badBehaviorPagination.pageNo === 1" @click="changeBadBehaviorPage(badBehaviorPagination.pageNo - 1)">上一页</button>
                          <button class="page-btn" :disabled="badBehaviorPagination.pageNo >= Math.ceil(badBehaviorPagination.total / badBehaviorPagination.pageSize)" @click="changeBadBehaviorPage(badBehaviorPagination.pageNo + 1)">下一页</button>
                          <button class="page-btn" :disabled="badBehaviorPagination.pageNo >= Math.ceil(badBehaviorPagination.total / badBehaviorPagination.pageSize)" @click="changeBadBehaviorPage(Math.ceil(badBehaviorPagination.total / badBehaviorPagination.pageSize))">末页</button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 7. 良好行为 -->
                <div v-if="activePersonnelTab === 6" class="tab-content good-behavior">
                  <!-- <h4 class="section-title">良好行为</h4> -->
                  <div v-if="goodBehaviorLoading" class="loading">
                    <div class="loading-spinner"></div>
                    <p>加载中...</p>
                  </div>
                  <div v-else>
                    <div class="data-table-container">
                      <table class="data-table">
                        <thead>
                          <tr>
                            <th>序号</th>
                            <th>时间</th>
                            <th>良好行为内容</th>
                            <th>行为结果</th>
                            <th>附件</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(item, index) in goodBehaviorData" :key="index">
                            <td>{{ (goodBehaviorPagination.pageNo - 1) * goodBehaviorPagination.pageSize + index + 1 }}</td>
                            <td>{{ formatDate(item.behaviorDate) }}</td>
                            <td>{{ item.behaviorContent || '-' }}</td>
                            <td>{{ item.behaviorResult || '-' }}</td>
                            <td>
                              <div v-if="item.behaviorFileUrl" class="attachment-link">
                                <a :href="item.behaviorFileUrl" target="_blank">查看附件</a>
                              </div>
                              <span v-else class="no-attachment">-</span>
                            </td>
                          </tr>
                          <tr v-if="goodBehaviorData.length === 0">
                            <td colspan="5" class="no-data">暂无良好行为记录</td>
                          </tr>
                        </tbody>
                      </table>
                      <!-- 良好行为分页 -->
                      <div v-if="goodBehaviorPagination.total > 0" class="pagination-container">
                        <div class="pagination-info">
                          共 {{ goodBehaviorPagination.total }} 条数据，第 {{ goodBehaviorPagination.pageNo }} / {{ Math.ceil(goodBehaviorPagination.total / goodBehaviorPagination.pageSize) }} 页
                        </div>
                        <div class="pagination-controls">
                          <button class="page-btn" :disabled="goodBehaviorPagination.pageNo === 1" @click="changeGoodBehaviorPage(1)">首页</button>
                          <button class="page-btn" :disabled="goodBehaviorPagination.pageNo === 1" @click="changeGoodBehaviorPage(goodBehaviorPagination.pageNo - 1)">上一页</button>
                          <button class="page-btn" :disabled="goodBehaviorPagination.pageNo >= Math.ceil(goodBehaviorPagination.total / goodBehaviorPagination.pageSize)" @click="changeGoodBehaviorPage(goodBehaviorPagination.pageNo + 1)">下一页</button>
                          <button class="page-btn" :disabled="goodBehaviorPagination.pageNo >= Math.ceil(goodBehaviorPagination.total / goodBehaviorPagination.pageSize)" @click="changeGoodBehaviorPage(Math.ceil(goodBehaviorPagination.total / goodBehaviorPagination.pageSize))">末页</button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 8. 健康信息 -->
                <div v-if="activePersonnelTab === 7" class="tab-content health-info">
                  <!-- <h4 class="section-title">健康信息</h4> -->
                  <div v-if="healthInfoLoading" class="loading">
                    <div class="loading-spinner"></div>
                    <p>加载中...</p>
                  </div>
                  <div v-else>
                    <div class="data-table-container">
                      <table class="data-table">
                        <thead>
                          <tr>
                            <th>序号</th>
                            <th>体检时间</th>
                            <th>体检结论</th>
                            <th>附件</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(item, index) in healthInfoData" :key="index">
                            <td>{{ (healthInfoPagination.pageNo - 1) * healthInfoPagination.pageSize + index + 1 }}</td>
                            <td>{{ formatDate(item.healthDate) }}</td>
                            <td>
                              <span :class="['status-badge', item.healthStatus === '1' ? 'status-qualified' : 'status-unqualified']">
                                {{ item.healthStatus === '1' ? '合格' : item.healthStatus === '0' ? '不合格' : '-' }}
                              </span>
                            </td>
                            <td>
                              <div v-if="item.healthFileUrl" class="attachment-link">
                                <a :href="item.healthFileUrl" target="_blank">查看附件</a>
                              </div>
                              <span v-else class="no-attachment">-</span>
                            </td>
                          </tr>
                          <tr v-if="healthInfoData.length === 0">
                            <td colspan="4" class="no-data">暂无健康信息记录</td>
                          </tr>
                        </tbody>
                      </table>
                      <!-- 健康信息分页 -->
                      <div v-if="healthInfoPagination.total > 0" class="pagination-container">
                        <div class="pagination-info">
                          共 {{ healthInfoPagination.total }} 条数据，第 {{ healthInfoPagination.pageNo }} / {{ Math.ceil(healthInfoPagination.total / healthInfoPagination.pageSize) }} 页
                        </div>
                        <div class="pagination-controls">
                          <button class="page-btn" :disabled="healthInfoPagination.pageNo === 1" @click="changeHealthInfoPage(1)">首页</button>
                          <button class="page-btn" :disabled="healthInfoPagination.pageNo === 1" @click="changeHealthInfoPage(healthInfoPagination.pageNo - 1)">上一页</button>
                          <button class="page-btn" :disabled="healthInfoPagination.pageNo >= Math.ceil(healthInfoPagination.total / healthInfoPagination.pageSize)" @click="changeHealthInfoPage(healthInfoPagination.pageNo + 1)">下一页</button>
                          <button class="page-btn" :disabled="healthInfoPagination.pageNo >= Math.ceil(healthInfoPagination.total / healthInfoPagination.pageSize)" @click="changeHealthInfoPage(Math.ceil(healthInfoPagination.total / healthInfoPagination.pageSize))">末页</button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="no-person-selected">
              <p>未选择人员信息</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  <script setup>
  import { onMounted, ref, watch, computed } from "vue";
  import { useRouter, useRoute } from "vue-router";
  import { useI18n } from 'vue-i18n';
  import item1 from "./components/item1.vue";
  import item2 from "./components/item2.vue";
  import item3 from "./components/item3.vue";
  import request from "@/utils/request";
  import defaultMapImg from '@/assets/images/project/mapImg.png';
  
  const router = useRouter();
      const route = useRoute();
      const { t } = useI18n();
      
      const title = ref("");
  const projectName = localStorage.getItem("clickProject");
  const projectData = ref({});
  const personData = ref({}); // 新增人员数据响应式变量

// 人员弹窗相关
const showModal = ref(false);
const personnelData = ref([]);
const modalLoading = ref(false);
const pagination = ref({
  pageNo: 1,
  pageSize: 10,
  total: 0
});

// 人员详情列表弹窗相关
const showPersonnelDetailModal = ref(false);
const currentPersonDetail = ref(null);

// 8个标签页相关数据
const personnelTabs = ref([
  { id: 1, name: '基本信息' },
  { id: 2, name: '岗位信息' },
  { id: 3, name: '资格证书' },
  { id: 4, name: '评价信息' },
  { id: 5, name: '安全教育' },
  { id: 6, name: '不良行为' },
  { id: 7, name: '良好行为' },
  { id: 8, name: '健康信息' }
]);

const activePersonnelTab = ref(0); // 当前激活的标签页索引

// 资格证书相关数据
const certificateData = ref([]);
const certificateLoading = ref(false);

// 评价信息相关数据
const evaluationData = ref([]);
const evaluationLoading = ref(false);
const evaluationPagination = ref({
  pageNo: 1,
  pageSize: 10,
  total: 0
});

// 安全教育相关数据
const safetyEducationData = ref([]);
const safetyEducationLoading = ref(false);
const safetyEducationPagination = ref({
  pageNo: 1,
  pageSize: 10,
  total: 0
});

// 不良行为相关数据
const badBehaviorData = ref([]);
const badBehaviorLoading = ref(false);
const badBehaviorPagination = ref({
  pageNo: 1,
  pageSize: 10,
  total: 0
});

// 良好行为相关数据
const goodBehaviorData = ref([]);
const goodBehaviorLoading = ref(false);
const goodBehaviorPagination = ref({
  pageNo: 1,
  pageSize: 10,
  total: 0
});

// 健康信息相关数据
const healthInfoData = ref([]);
const healthInfoLoading = ref(false);
const healthInfoPagination = ref({
  pageNo: 1,
  pageSize: 10,
  total: 0
});
  
  const isChina = ref(localStorage.getItem("isChina") ? true : false);
  
  // 判断是否为国际项目
  const isInternationalProject = ref(false);
  
  // 新增：判断是否为设计类项目
  const isDesignProject = computed(() => {
    return projectData.value && projectData.value.项目类别 === '设计';
  });
  
  const btns = ref([
    {
      id: 1,
      name: t('projectDetail.tabs.overview'),
    },
    {
      id: 2,
      name: t('projectDetail.tabs.qualitySafety'),
    },
    {
      id: 3,
      name: t('projectDetail.tabs.equipmentFacilities'),
    },
  ]);
  
  // 计算过滤后的按钮列表
  const filteredBtns = computed(() => {
    return btns.value.map(btn => ({
      ...btn,
      name: btn.id === 1 ? t('projectDetail.tabs.overview') :
        btn.id === 2 ? t('projectDetail.tabs.qualitySafety') :
          btn.id === 3 ? t('projectDetail.tabs.equipmentFacilities') :
            btn.name
    }));
  });
  
  const btnsActive = ref(0);
  const btnsChange = (val) => {
    btnsActive.value = val;
    localStorage.setItem("btnsActive", val);
  };

  // 新增处理按钮点击逻辑
  const handleBtnClick = (index) => {
    // 对于国际项目或设计类项目，只允许点击第一个标签（项目概况）
    if ((isInternationalProject.value || isDesignProject.value) && index > 0) {
      return; // 不做任何操作，禁止切换
    }
    btnsChange(index);
  };
  
  const images = ref([]);
  const currentImageIndex = ref(0);
  // 添加标识是否使用默认图片
  const isUsingDefaultImage = ref(true);

  // 添加视频相关变量
  const videos = ref([]);
  const mediaItems = ref([]); // 合并的媒体项目（照片+视频）
  const currentMediaIndex = ref(0);
  const currentMediaType = ref('image'); // 'image' 或 'video'
  
  // 右侧图片源
  const rightImageSrc = computed(() => {
    if (currentMediaType.value === 'image' && images.value.length > 0) {
      return images.value[currentImageIndex.value];
    }
    return null;
  });

  // 右侧视频源  
  const rightVideoSrc = computed(() => {
    if (currentMediaType.value === 'video' && videos.value.length > 0) {
      return videos.value[currentMediaIndex.value - images.value.length];
    }
    return null;
  });
  
    // 已移除未使用的 generateImageUrls 函数
  
  // 照片处理函数
  async function getProjectPhotoUrls(photoIds) {
    if (!photoIds) {
      return [defaultMapImg];
    }
  
    try {
      // 先获取token
      const tokenRes = await request.get("/globalManage/zjmanage/largescreen/getToken");
  
      if (tokenRes.code !== 0 || !tokenRes.data) {
        console.error("获取token失败:", tokenRes);
        return [defaultMapImg];
      }
  
      const token = tokenRes.data;
      const userId = "941981453197164545"; // 固定的userId
  
      // 处理照片ID列表
      const photoIdArray = photoIds.split(',').filter(id => id.trim());
  
      if (photoIdArray.length === 0) {
        return [defaultMapImg];
      }
  
      const photoUrls = [];
  
      // 限制处理最多5张图片
      const maxPhotos = Math.min(photoIdArray.length, 5);
  
      // 验证每个图片URL是否可访问
      for (let i = 0; i < maxPhotos; i++) {
        const photoId = photoIdArray[i].trim();
        if (photoId) {
          try {
            // 构建照片URL
            const photoUrl = `http://192.168.15.61:20600/papi/openapi/api/file/v2/common/download/${photoId}?access_token=${token}&userid=${userId}`;
  
            // 验证图片是否可访问
            const isValid = await validateImageUrl(photoUrl);
            if (isValid) {
              photoUrls.push(photoUrl);
            }
          } catch (err) {
            console.warn(`图片${photoId}处理失败:`, err);
            continue;
          }
        }
      }
  
      return photoUrls.length > 0 ? photoUrls : [defaultMapImg];
    } catch (err) {
      console.error("处理照片失败:", err);
      return [defaultMapImg];
    }
  }
  
  // 验证图片URL是否可访问
  function validateImageUrl(url) {
    return new Promise((resolve) => {
      const img = new Image();
      const timeout = setTimeout(() => {
        resolve(false);
      }, 5000); // 5秒超时
  
      img.onload = () => {
        clearTimeout(timeout);
        resolve(true);
      };
  
      img.onerror = () => {
        clearTimeout(timeout);
        resolve(false);
      };
  
      img.src = url;
    });
  }

  // 视频处理函数
  async function getProjectVideoUrls(videoIds) {
    console.log('🎥 开始处理视频，videoIds:', videoIds);
    
    if (!videoIds) {
      console.log('❌ videoIds为空，返回空数组');
      return [];
    }

    try {
      // 先获取token
      console.log('🔑 开始获取token...');
      const tokenRes = await request.get("/globalManage/zjmanage/largescreen/getToken");
      console.log('🔑 token响应:', tokenRes);

      if (tokenRes.code !== 0 || !tokenRes.data) {
        console.error("❌ 获取token失败:", tokenRes);
        return [];
      }

      const token = tokenRes.data;
      const userId = "941981453197164545"; // 固定的userId
      console.log('✅ token获取成功:', token);

      // 处理视频ID列表
      const videoIdArray = videoIds.split(',').filter(id => id.trim());
      console.log('📋 处理后的视频ID数组:', videoIdArray);

      if (videoIdArray.length === 0) {
        console.log('❌ 视频ID数组为空，返回空数组');
        return [];
      }

          // 限制处理最多5个视频
    const maxVideos = Math.min(videoIdArray.length, 5);
    console.log(`🎬 准备处理 ${maxVideos} 个视频`);

    // 直接构建视频URL（不验证，跟item3.vue保持一致）
    const videoUrls = videoIdArray.slice(0, maxVideos).map(videoId => {
      const trimmedId = videoId.trim();
      const videoUrl = `http://192.168.15.61:20600/papi/openapi/api/file/v2/common/download/${trimmedId}?access_token=${token}&userid=${userId}`;
      console.log(`🔗 构建的视频URL: ${videoUrl}`);
      return videoUrl;
    });

      console.log(`🎉 最终返回的视频URLs:`, videoUrls);
      return videoUrls;
    } catch (err) {
      console.error("❌ 处理视频失败:", err);
      return [];
    }
  }



  // 合并媒体项目的函数
  function mergeMediaItems() {
    const items = [];
    
    // 1. 处理效果图（如果存在）
    if (projectData.value && projectData.value.项目效果图) {
      // 处理效果图ID列表（可能用逗号分割）
      const effectImageIds = projectData.value.项目效果图.split(',').filter(id => id.trim());
      console.log('发现效果图ID列表:', effectImageIds);
      
      // 查找所有效果图URL并添加为优先项
      effectImageIds.forEach((effectImageId, index) => {
        const trimmedId = effectImageId.trim();
        const effectImageUrl = images.value.find(imgUrl => imgUrl.includes(trimmedId));
        
        if (effectImageUrl) {
          // 添加效果图作为优先项
          items.push({
            type: 'image',
            url: effectImageUrl,
            index: items.length,
            thumbnail: effectImageUrl,
            isEffectImage: true // 标记为效果图
          });
          console.log(`添加效果图${index + 1}:`, effectImageUrl);
        } else {
          console.log(`未找到对应效果图URL，效果图ID为:`, trimmedId);
        }
      });
    } else {
      console.log('项目数据中没有效果图ID');
    }
    
    // 2. 添加其他普通图片（排除已添加的效果图）
    const effectImageUrls = items.filter(item => item.isEffectImage).map(item => item.url);
    
    images.value.forEach((image) => {
      // 如果当前图片是效果图，则跳过（避免重复添加）
      if (effectImageUrls.includes(image)) {
        return;
      }
      
      items.push({
        type: 'image',
        url: image,
        index: items.length,
        thumbnail: image
      });
    });
    
    // 3. 添加视频项目（放在最后）
    if (videos.value.length > 0) {
      console.log(`添加${videos.value.length}个视频项目`);
      videos.value.forEach((video) => {
        items.push({
          type: 'video',
          url: video,
          index: items.length,
          thumbnail: video // 视频缩略图使用视频本身
        });
      });
    } else {
      console.log('没有视频项目可添加');
    }
    
    mediaItems.value = items;
    console.log(`合并后的媒体项目共${items.length}个:`, mediaItems.value);
    
    // 如果有媒体项目，设置第一个为活动状态
    if (items.length > 0) {
      currentMediaIndex.value = 0;
      const firstItem = items[0];
      if (firstItem.type === 'image') {
        currentImageIndex.value = 0;
        currentMediaType.value = 'image';
      } else if (firstItem.type === 'video') {
        currentMediaType.value = 'video';
      }
      
      // 初始化显示第一个媒体项目
      setTimeout(() => {
        handleThumbnailClick(0, firstItem);
      }, 100);
    }
  }
  
  // 获取项目数据
  async function fetchProjectData() {
    try {
      let projectDatas = {};
      let queryData = {}
      try {
        // 从sessionStorage获取项目数据而不是URL参数
        queryData = JSON.parse(sessionStorage.getItem("clickProject") || "{}");
      } catch (e) {
        console.error("解析项目数据失败", e);
      }
      const projectId = queryData.code || "";
      const isGwParam = queryData.isGw || "false";

      // 转换isGw参数，确保是字符串类型
      const isGw = String(isGwParam).toLowerCase();

      // 设置国际项目标识
      isInternationalProject.value = isGw === "true";

      let res;
      if (isGw === "true") {
        // 国外项目接口
        res = await request.get("/globalManage/zjmanage/largescreen/getXmxxV2", {
          params: { id: projectId }
        });
      } else {
        // 国内项目接口
        res = await request.get("/globalManage/zjmanage/largescreen/getXmjbxx", {
          params: { id: projectId }
        });
      }

      if (res.code === 0 && res.data && res.data.length > 0) {
        const data = res.data[0];

        if (isGw === "true") {
          // 设置标题为国家名
          title.value = data.国家 || "";
          projectData.value = data;
          
          // 国际项目：使用原有照片逻辑
          await processProjectPhotos(data);
          
          // 确保国际项目的效果图和视频都能被处理
          console.log('正在处理国际项目数据');
          console.log('项目效果图:', data.项目效果图);
          console.log('项目视频:', data.项目视频);
        } else {
          // 国内项目数据映射
          projectDatas = {
            项目名称: data.项目名称 || "",
            合同金额: data.合同金额 || "0",
            产值: data.产值 || "0",
            工程回款总额: data.工程回款总额 || "0",
            项目进度: data.施工进度 ? (Number(data.施工进度) * 100) + '%' : '0%',
            开工日期: data.开工日期 || "",
            总工期: data.总工期 || "",
            照片: data.项目照片 || "",
            视频: data.项目视频 || "",
            项目总人数: data.项目总人数 || "0",
            正式员工: data.正式员工 || "0",
            劳务派遣: data.劳务派遣 || "0",
            其他形式用工: data.其他形式用工 || "0",
            合作单位: data.合作单位人员 || "0",
            类型: "项目",
            省: data.省 || "",
            市: data.市 || "",
            区: data.区 || "",
            项目经度: data.项目经度 || "",
            项目纬度: data.项目纬度 || "",
            code: data.code || "" // 确保code字段传递给项目数据
          };

          // 设置标题为省份名
          title.value = data.省 || "";
          projectData.value = data;

          // 国内项目：调用渲染接口获取渲染文件
          if (data.code) {
            try {
              const renderingRes = await request.post("/globalManage/zjmanage/largescreen/getRenderingByCode", {
                projectCode: data.code
              });
              
              if (renderingRes.code === 0 && renderingRes.data && renderingRes.data.length > 0) {
                console.log("获取渲染文件成功:", renderingRes.data);
                // 将渲染文件信息添加到项目数据中
                projectData.value.renderingFiles = renderingRes.data;
                
                // 提取渲染文件的下载链接作为展示图片
                const renderingImages = renderingRes.data
                  .filter(file => file.downloadUrl) // 过滤掉没有下载链接的文件
                  .map(file => file.downloadUrl);
                
                if (renderingImages.length > 0) {
                  // 使用渲染文件作为展示图片
                  images.value = renderingImages;
                  isUsingDefaultImage.value = false;
                  console.log("使用渲染文件作为展示图片:", renderingImages);
                } else {
                  // 如果渲染文件没有有效的下载链接，则使用原有照片逻辑
                  await processProjectPhotos(data);
                }
              } else {
                console.warn("获取渲染文件失败或无数据:", renderingRes);
                projectData.value.renderingFiles = [];
                // 没有渲染文件时使用原有照片逻辑
                await processProjectPhotos(data);
              }
            } catch (renderingErr) {
              console.error("调用渲染接口失败:", renderingErr);
              projectData.value.renderingFiles = [];
              // 接口调用失败时使用原有照片逻辑
              await processProjectPhotos(data);
            }
          } else {
            console.warn("项目数据中没有code字段，跳过渲染接口调用");
            projectData.value.renderingFiles = [];
            // 没有code字段时使用原有照片逻辑
            await processProjectPhotos(data);
          }
        }

        // 保存视频信息
        if (data.项目视频) {
          // 可以在这里处理视频信息，如果需要的话
          // videoUrl.value = data.项目视频;
        }
      } else {
        // 接口返回错误，设置默认图片
        images.value = [defaultMapImg];
        isUsingDefaultImage.value = true;
      }
    } catch (err) {
      console.error("获取项目数据失败:", err);
      // 接口异常，设置默认图片
      images.value = [defaultMapImg];
      isUsingDefaultImage.value = true;
    }
  }

  // 处理项目照片的独立函数
  async function processProjectPhotos(data) {
    console.log('processProjectPhotos收到的原始data:', data);
    console.log('data.项目照片:', data.项目照片);
    console.log('data.项目效果图:', data.项目效果图);
    console.log('data.项目视频:', data.项目视频);
    
    // 用于跟踪是否找到了任何媒体内容
    let hasAnyMedia = false;
    
    // 处理照片
    if (data.项目照片) {
      try {
        // 使用新的照片处理函数
        const photoUrls = await getProjectPhotoUrls(data.项目照片);

        // 检查是否获取到有效的图片URL
        if (photoUrls.length > 0 && photoUrls[0] !== defaultMapImg) {
          images.value = photoUrls;
          hasAnyMedia = true; // 找到了有效的照片
        } else {
          // 如果处理失败，设为空数组
          images.value = [];
        }
      } catch (err) {
        console.error("处理项目图片失败:", err);
        images.value = [];
      }
    } else {
      // 没有项目照片字段
      images.value = [];
    }

    // 处理项目效果图（如果有）
    let hasEffectImage = false;
    if (data.项目效果图) {
      try {
        // 获取token
        const tokenRes = await request.get("/globalManage/zjmanage/largescreen/getToken");
        if (tokenRes.code === 0 && tokenRes.data) {
          const token = tokenRes.data;
          const userId = "941981453197164545"; // 固定的userId
          
          // 处理效果图ID列表（可能用逗号分割）
          const effectImageIds = data.项目效果图.split(',').filter(id => id.trim());
          console.log("效果图ID列表:", effectImageIds);
          
          // 逐个处理效果图
          for (let i = 0; i < effectImageIds.length; i++) {
            const effectImageId = effectImageIds[i].trim();
            if (effectImageId) {
              const effectImageUrl = `http://192.168.15.61:20600/papi/openapi/api/file/v2/common/download/${effectImageId}?access_token=${token}&userid=${userId}`;
              
              // 验证效果图URL是否有效
              const isValid = await validateImageUrl(effectImageUrl);
              if (isValid) {
                console.log(`效果图${i + 1}URL有效:`, effectImageUrl);
                
                // 检查images数组中是否已包含此效果图
                const exists = images.value.some(img => img === effectImageUrl);
                if (!exists) {
                  // 如果不存在，则添加到images数组，后续会在mergeMediaItems中特殊处理
                  images.value.push(effectImageUrl);
                  hasEffectImage = true;
                  hasAnyMedia = true; // 找到了有效的效果图
                  console.log(`效果图${i + 1}已添加到图片列表`);
                }
              } else {
                console.warn(`效果图${i + 1}URL无效:`, effectImageUrl);
              }
            }
          }
        }
      } catch (err) {
        console.error("处理项目效果图失败:", err);
      }
    }

    // 处理项目视频
    let hasVideos = false;
    if (data.项目视频) {
      try {
        const videoUrls = await getProjectVideoUrls(data.项目视频);
        if (videoUrls.length > 0) {
          videos.value = videoUrls;
          hasVideos = true;
          hasAnyMedia = true; // 找到了有效的视频
          console.log("获取项目视频成功:", videoUrls);
        } else {
          videos.value = [];
        }
      } catch (err) {
        console.error("处理项目视频失败:", err);
        videos.value = [];
      }
    } else {
      videos.value = [];
      console.log('未发现项目视频');
    }

    // 只有在三种媒体都不存在的情况下，才使用默认图片
    if (!hasAnyMedia) {
      console.log('没有找到任何媒体内容，使用默认图片');
      images.value = [defaultMapImg];
      isUsingDefaultImage.value = true;
    } else {
      console.log(`找到媒体内容: 照片=${images.value.length > 0}, 效果图=${hasEffectImage}, 视频=${hasVideos}`);
      isUsingDefaultImage.value = false;
    }

    console.log('处理后的images数组:', images.value);

    // 合并媒体项目
    mergeMediaItems();
  }

  // 获取国内项目人员数据
  async function getPersonData() {
    try {
      const queryData = JSON.parse(sessionStorage.getItem("clickProject") || "{}");
      const projectCode = queryData.code || "";

      if (!projectCode) {
        console.warn("项目code为空，无法获取人员数据");
        return {};
      }

      // 使用POST方法调用接口
      const res = await request.post("/globalManage/deviceMonitor/getNumberPerson", {
        projectCode: projectCode
      });

      console.log("获取人员数据响应:", res);

      if (res.code === 0 && res.data) {
        return res.data;
      } else {
        console.warn("获取人员数据失败或无数据:", res);
        return {};
      }
    } catch (err) {
      console.error("获取人员数据失败:", err);
      return {};
    }
  }
  
  onMounted(() => {
    // 使用新的统一接口获取数据
    fetchProjectData().then(() => {
      // 在项目数据获取完成后，判断是否为国内项目，只有国内项目才获取人员数据
      const queryData = JSON.parse(sessionStorage.getItem("clickProject") || "{}");
      const isGw = queryData.isGw === "true";
      
      if (!isGw) {
        // 国内项目：获取人员数据
        getPersonData().then(data => {
          personData.value = data;
          console.log("国内项目人员数据:", data);
        });
      }
    });
  });

  watch([images, videos], () => {
    mergeMediaItems();
  });

/**
 * 返回上一级 - 返回到国家/地区详情页面
 * 特殊处理：江苏省项目返回到市一级，其他省份项目返回到省一级
 */
   // 显示人员弹窗
  const showPersonnelModal = async () => {
    showModal.value = true;
    // 重置分页到第一页
    pagination.value.pageNo = 1;
    await fetchPersonnelData();
  };

  // 获取人员列表数据
  const fetchPersonnelData = async () => {
    modalLoading.value = true;
    
    try {
      // 获取项目代码
      const projectCode = projectData.value?.code || projectData.value?.项目代码;
      
      if (!projectCode) {
        console.error('项目代码未找到，无法获取人员数据');
        return;
      }

      console.log('正在获取项目人员列表，项目代码:', projectCode);
      
      // 调用人员列表接口
      const response = await request.post("/globalManage/person/queryPersonPageList", {
        pageNo: pagination.value.pageNo,
        pageSize: pagination.value.pageSize,
        projectCode: projectCode,
        buildUnitId: "",
        teamsId: "",
        personStatus: ""
      });
      
      console.log('人员列表接口响应:', response);
      
      if (response.code === 0 && response.data) {
        personnelData.value = response.data.list || [];
        pagination.value.total = response.data.total || 0;
        console.log('获取人员列表成功，总数:', pagination.value.total);
      } else {
        console.error('获取人员列表失败:', response);
        personnelData.value = [];
        pagination.value.total = 0;
      }
    } catch (error) {
      console.error('获取人员列表异常:', error);
      personnelData.value = [];
      pagination.value.total = 0;
    } finally {
      modalLoading.value = false;
    }
  };

  // 关闭弹窗
  const closeModal = () => {
    showModal.value = false;
  };

  // 脱敏身份证号码
  const maskIdCard = (idCard) => {
    if (!idCard) return '-';
    if (idCard.length < 8) return idCard;
    return idCard.substring(0, 6) + '****' + idCard.substring(idCard.length - 4);
  };

  // 格式化日期显示
  const formatDate = (dateStr) => {
    if (!dateStr) return '-';
    if (dateStr.includes(' ')) {
      return dateStr.split(' ')[0];
    }
    return dateStr;
  };

  // 组合地址信息
  const getPersonAddress = (person) => {
    if (!person) return '-';
    const addressParts = [
      person.provinceName,
      person.cityName,
      person.areaName
    ].filter(Boolean);
    return addressParts.length > 0 ? addressParts.join('') : '-';
  };

  // 切换页码
  const changePage = async (page) => {
    if (page < 1 || page > Math.ceil(pagination.value.total / pagination.value.pageSize)) {
      return;
    }
    pagination.value.pageNo = page;
    await fetchPersonnelData();
  };

  // 获取页码数组
  const getPageNumbers = () => {
    const totalPages = Math.ceil(pagination.value.total / pagination.value.pageSize);
    const currentPage = pagination.value.pageNo;
    const pageNumbers = [];
    
    // 最多显示7个页码
    const maxVisible = 7;
    let start = Math.max(1, currentPage - Math.floor(maxVisible / 2));
    let end = Math.min(totalPages, start + maxVisible - 1);
    
    // 如果end页码不足maxVisible，调整start
    if (end - start + 1 < maxVisible) {
      start = Math.max(1, end - maxVisible + 1);
    }
    
    for (let i = start; i <= end; i++) {
      pageNumbers.push(i);
    }
    
    return pageNumbers;
  };

  // 查看照片
  const viewPhoto = (photo) => {
    // 这里可以实现照片预览功能
    console.log('查看照片:', photo);
  };

  // 查看二维码
  const viewQRCode = (qrCode) => {
    // 这里可以实现二维码预览功能
    console.log('查看二维码:', qrCode);
  };

  // 查看人员详情
  const viewPersonDetail = (person) => {
    // 保存当前人员信息并打开详情弹窗
    currentPersonDetail.value = person;
    // 重置标签页状态到基本信息
    activePersonnelTab.value = 0;
    // 清空所有标签页数据
    certificateData.value = [];
    evaluationData.value = [];
    safetyEducationData.value = [];
    badBehaviorData.value = [];
    goodBehaviorData.value = [];
    healthInfoData.value = [];
    // 重置所有分页状态
    evaluationPagination.value = { pageNo: 1, pageSize: 10, total: 0 };
    safetyEducationPagination.value = { pageNo: 1, pageSize: 10, total: 0 };
    badBehaviorPagination.value = { pageNo: 1, pageSize: 10, total: 0 };
    goodBehaviorPagination.value = { pageNo: 1, pageSize: 10, total: 0 };
    healthInfoPagination.value = { pageNo: 1, pageSize: 10, total: 0 };
    // 隐藏人员列表弹窗，显示人员详情弹窗
    showModal.value = false;
    showPersonnelDetailModal.value = true;
    console.log('查看人员详情:', person);
  };

  // 关闭人员详情弹窗
  const closePersonnelDetailModal = () => {
    console.log('关闭人员详情弹窗被调用');
    showPersonnelDetailModal.value = false;
    currentPersonDetail.value = null;
    // 返回到人员列表弹窗
    showModal.value = true;
  };

  // 从人员详情弹窗返回到人员列表弹窗
  const backToPersonnelList = () => {
    showPersonnelDetailModal.value = false;
    currentPersonDetail.value = null;
    // 返回到人员列表弹窗
    showModal.value = true;
  };

  // 切换标签页
  const switchPersonnelTab = async (tabIndex) => {
    activePersonnelTab.value = tabIndex;
    
    // 根据标签页索引加载对应数据
    if (!currentPersonDetail.value) return;
    
    const personId = currentPersonDetail.value.personId;
    
    switch (tabIndex) {
      case 2: // 资格证书
        await fetchCertificateData(personId);
        break;
      case 3: // 评价信息
        evaluationPagination.value.pageNo = 1;
        await fetchEvaluationData(personId);
        break;
      case 4: // 安全教育
        safetyEducationPagination.value.pageNo = 1;
        await fetchSafetyEducationData(personId);
        break;
      case 5: // 不良行为
        badBehaviorPagination.value.pageNo = 1;
        await fetchBadBehaviorData(personId);
        break;
      case 6: // 良好行为
        goodBehaviorPagination.value.pageNo = 1;
        await fetchGoodBehaviorData(personId);
        break;
      case 7: // 健康信息
        healthInfoPagination.value.pageNo = 1;
        await fetchHealthInfoData(personId);
        break;
    }
  };

  // 获取资格证书数据
  const fetchCertificateData = async (personId) => {
    if (!personId) return;
    
    certificateLoading.value = true;
    try {
      // 使用人员证件信息查询接口
      const response = await request.post("/globalManage/person/queryPersonCertificate", {
        personId: personId
      });
      
      if (response.code === 0 && response.data) {
        // 将单个证书对象转换为数组格式以便展示
        certificateData.value = response.data ? [response.data] : [];
      } else {
        certificateData.value = [];
        console.warn('获取资格证书数据失败:', response);
      }
    } catch (error) {
      console.error('获取资格证书数据异常:', error);
      certificateData.value = [];
    } finally {
      certificateLoading.value = false;
    }
  };

  // 获取评价信息数据
  const fetchEvaluationData = async (personId) => {
    if (!personId) return;
    
    evaluationLoading.value = true;
    try {
      const response = await request.post("/globalManage/person/queryEvaluateRecord", {
        pageNo: evaluationPagination.value.pageNo,
        pageSize: evaluationPagination.value.pageSize,
        projectCode: projectData.value?.code || projectData.value?.项目代码 || "",
        personId: personId
      });
      
      if (response.code === 0 && response.data) {
        // 处理返回的数据，添加type字段用于显示
        const processedData = (response.data.list || []).map(item => ({
          ...item,
          type: item.evaluateStatus === '1' ? '奖励' : item.evaluateStatus === '0' ? '惩罚' : '-',
          memo: item.memo || (item.evaluateStatus === '1' ? '奖励记录' : item.evaluateStatus === '0' ? '惩罚记录' : '-')
        }));
        evaluationData.value = processedData;
        evaluationPagination.value.total = response.data.total || 0;
      } else {
        evaluationData.value = [];
        evaluationPagination.value.total = 0;
        console.warn('获取评价信息数据失败:', response);
      }
    } catch (error) {
      console.error('获取评价信息数据异常:', error);
      evaluationData.value = [];
      evaluationPagination.value.total = 0;
    } finally {
      evaluationLoading.value = false;
    }
  };

  // 获取安全教育数据
  const fetchSafetyEducationData = async (personId) => {
    if (!personId) return;
    
    safetyEducationLoading.value = true;
    try {
      const response = await request.post("/globalManage/person/queryTrainingRecord", {
        pageNo: safetyEducationPagination.value.pageNo,
        pageSize: safetyEducationPagination.value.pageSize,
        personId: personId
      });
      
      if (response.code === 0 && response.data) {
        safetyEducationData.value = response.data.list || [];
        safetyEducationPagination.value.total = response.data.total || 0;
      } else {
        safetyEducationData.value = [];
        safetyEducationPagination.value.total = 0;
        console.warn('获取安全教育数据失败:', response);
      }
    } catch (error) {
      console.error('获取安全教育数据异常:', error);
      safetyEducationData.value = [];
      safetyEducationPagination.value.total = 0;
    } finally {
      safetyEducationLoading.value = false;
    }
  };

  // 获取不良行为数据
  const fetchBadBehaviorData = async (personId) => {
    if (!personId) return;
    
    badBehaviorLoading.value = true;
    try {
      // 使用统一的行为记录查询接口，behaviorStatus: "0" 表示不良行为
      const response = await request.post("/globalManage/person/queryBehaviorRecord", {
        pageNo: badBehaviorPagination.value.pageNo,
        pageSize: badBehaviorPagination.value.pageSize,
        projectCode: projectData.value?.code || projectData.value?.项目代码 || "",
        personId: personId,
        behaviorStatus: "0"  // 0:不良行为
      });
      
      if (response.code === 0 && response.data) {
        badBehaviorData.value = response.data.list || [];
        badBehaviorPagination.value.total = response.data.total || 0;
      } else {
        badBehaviorData.value = [];
        badBehaviorPagination.value.total = 0;
        console.warn('获取不良行为数据失败:', response);
      }
    } catch (error) {
      console.error('获取不良行为数据异常:', error);
      badBehaviorData.value = [];
      badBehaviorPagination.value.total = 0;
    } finally {
      badBehaviorLoading.value = false;
    }
  };

  // 获取良好行为数据
  const fetchGoodBehaviorData = async (personId) => {
    if (!personId) return;
    
    goodBehaviorLoading.value = true;
    try {
      // 使用统一的行为记录查询接口，behaviorStatus: "1" 表示良好行为
      const response = await request.post("/globalManage/person/queryBehaviorRecord", {
        pageNo: goodBehaviorPagination.value.pageNo,
        pageSize: goodBehaviorPagination.value.pageSize,
        projectCode: projectData.value?.code || projectData.value?.项目代码 || "",
        personId: personId,
        behaviorStatus: "1"  // 1:良好行为
      });
      
      if (response.code === 0 && response.data) {
        goodBehaviorData.value = response.data.list || [];
        goodBehaviorPagination.value.total = response.data.total || 0;
      } else {
        goodBehaviorData.value = [];
        goodBehaviorPagination.value.total = 0;
        console.warn('获取良好行为数据失败:', response);
      }
    } catch (error) {
      console.error('获取良好行为数据异常:', error);
      goodBehaviorData.value = [];
      goodBehaviorPagination.value.total = 0;
    } finally {
      goodBehaviorLoading.value = false;
    }
  };

  // 获取健康信息数据
  const fetchHealthInfoData = async (personId) => {
    if (!personId) return;
    
    healthInfoLoading.value = true;
    try {
      // 获取项目代码
      const projectCode = projectData.value?.code || projectData.value?.项目代码 || "";
      if (!projectCode) {
        console.error('缺少项目代码');
        return;
      }

      // 使用健康信息记录分页列表API
      const response = await request.post("/globalManage/person/queryHealthRecord", {
        pageNo: healthInfoPagination.value.pageNo,
        pageSize: healthInfoPagination.value.pageSize,
        projectCode: projectCode,
        personId: personId
      });
      
      if (response.code === 0 && response.data) {
        healthInfoData.value = response.data.list || [];
        healthInfoPagination.value.total = response.data.total || 0;
      } else {
        healthInfoData.value = [];
        healthInfoPagination.value.total = 0;
        console.warn('获取健康信息数据失败:', response);
      }
    } catch (error) {
      console.error('获取健康信息数据异常:', error);
      healthInfoData.value = [];
      healthInfoPagination.value.total = 0;
    } finally {
      healthInfoLoading.value = false;
    }
  };

  // 评价信息分页切换
  const changeEvaluationPage = async (page) => {
    if (page < 1 || page > Math.ceil(evaluationPagination.value.total / evaluationPagination.value.pageSize)) {
      return;
    }
    evaluationPagination.value.pageNo = page;
    await fetchEvaluationData(currentPersonDetail.value?.personId);
  };

  // 安全教育分页切换
  const changeSafetyEducationPage = async (page) => {
    if (page < 1 || page > Math.ceil(safetyEducationPagination.value.total / safetyEducationPagination.value.pageSize)) {
      return;
    }
    safetyEducationPagination.value.pageNo = page;
    await fetchSafetyEducationData(currentPersonDetail.value?.personId);
  };

  // 不良行为分页切换
  const changeBadBehaviorPage = async (page) => {
    if (page < 1 || page > Math.ceil(badBehaviorPagination.value.total / badBehaviorPagination.value.pageSize)) {
      return;
    }
    badBehaviorPagination.value.pageNo = page;
    await fetchBadBehaviorData(currentPersonDetail.value?.personId);
  };

  // 良好行为分页切换
  const changeGoodBehaviorPage = async (page) => {
    if (page < 1 || page > Math.ceil(goodBehaviorPagination.value.total / goodBehaviorPagination.value.pageSize)) {
      return;
    }
    goodBehaviorPagination.value.pageNo = page;
    await fetchGoodBehaviorData(currentPersonDetail.value?.personId);
  };

  // 健康信息分页切换
  const changeHealthInfoPage = async (page) => {
    if (page < 1 || page > Math.ceil(healthInfoPagination.value.total / healthInfoPagination.value.pageSize)) {
      return;
    }
    healthInfoPagination.value.pageNo = page;
    await fetchHealthInfoData(currentPersonDetail.value?.personId);
  };

  const back = () => {
  console.log('项目详情页面返回上一级');
  
  // 构建要传递的数据
  const transferData = {
    level: '',
    title: ''
  };

  // 判断是国内还是国外项目
  const clickProject = sessionStorage.getItem("clickProject");
  if (clickProject) {
    try {
      const queryData = JSON.parse(clickProject || "{}");
      const isGw = queryData.isGw === "true";

      if (isGw) {
        // 国际项目：传递level和国家
        transferData.level = 'country';
        transferData.title = projectData.value.国家 || title.value;
        console.log(`国际项目返回到国家级: ${transferData.title}`);
      } else {
        // 国内项目：根据省份判断返回层级
        const province = projectData.value.省 || '';
        const city = projectData.value.市 || '';
        const district = projectData.value.区 || '';
        
        console.log(`项目所在地: 省=${province}, 市=${city}, 区=${district}`);
        
        // 标准化省份名称
        const normalizedProvince = normalizeProvinceName(province);
        console.log(`标准化后的省份名称: ${normalizedProvince}`);
        
        // 江苏省特殊处理：返回到市一级
        if (normalizedProvince === '江苏省') {
          if (city) {
            transferData.level = 'city';
            transferData.title = city;
            console.log(`江苏省项目返回到市级: ${city}`);
          } else {
            // 如果没有市级数据，退回到省级
            transferData.level = 'province';
            transferData.title = normalizedProvince;
            console.log(`江苏省项目缺少市级数据，返回到省级: ${normalizedProvince}`);
          }
        } else {
          // 其他省份：直接返回到省一级
          if (normalizedProvince) {
            transferData.level = 'province';
            transferData.title = normalizedProvince;
            console.log(`非江苏省项目返回到省级: ${normalizedProvince}`);
          } else {
            // 如果连省份都没有，尝试使用市级数据
            if (city) {
              transferData.level = 'city';
              transferData.title = city;
              console.log(`缺少省份数据，使用市级数据: ${city}`);
            } else if (district) {
              transferData.level = 'district';
              transferData.title = district;
              console.log(`缺少省市数据，使用区级数据: ${district}`);
            } else {
              console.warn('项目地理位置数据不完整，默认返回中国');
              transferData.level = 'country';
              transferData.title = '中国';
            }
          }
        }
      }
    } catch (e) {
      console.error("解析项目数据失败", e);
      // 默认返回中国
      transferData.level = 'country';
      transferData.title = '中国';
    }
  } else {
    console.warn('未找到项目数据，默认返回中国');
    transferData.level = 'country';
    transferData.title = '中国';
  }

  // 使用sessionStorage存储数据而不是通过URL参数
  sessionStorage.setItem("countryLevel", transferData.level);
  sessionStorage.setItem("countryTitle", transferData.title);
  // 新增：保存点击的国家/区域，便于业务布局页面恢复状态
  sessionStorage.setItem("clickCountry", transferData.title);
  // 新增：设置国内/国际项目标识（国内项目：1，国际项目：0）
  const isInternational = transferData.level === "country" && transferData.title !== "中国";
  localStorage.setItem("isChina", isInternational ? "0" : "1");
  
  // 设置返回上一级标记
  sessionStorage.setItem("returnType", "backToCountry");
  console.log('🔵 项目详情页面：设置returnType为backToCountry');
  console.log('🔵 当前sessionStorage状态:', {
    returnType: sessionStorage.getItem("returnType"),
    countryLevel: transferData.level,
    countryTitle: transferData.title,
    clickCountry: transferData.title,
    isChina: localStorage.getItem("isChina")
  });
  
  // 清理项目相关状态
  localStorage.removeItem("clickProject");
  sessionStorage.removeItem("clickProject");
  
  // 向iframe发送"cancel"消息（返回上一级的特定消息）
  setTimeout(() => {
    const iframe = document.getElementById("iframe");
    if (iframe && iframe.contentWindow) {
      console.log('向iframe发送返回上一级消息: cancel');
      iframe.contentWindow.postMessage(
        { eve: "cancel" },
        "*"
      );
    }
  }, 100);
  
  // 修改：跳转回业务布局首页
  router.replace({ path: "/business-display" });
};

/**
 * 标准化省份名称的辅助函数
 * @param {string} provinceName - 原始省份名称
 * @returns {string} - 标准化后的省份名称
 */
const normalizeProvinceName = (provinceName) => {
  if (!provinceName) return '';
  
  // 去除前后空格
  const trimmedName = provinceName.trim();
  
  // 标准化江苏省的各种表达方式
  if (trimmedName.includes('江苏')) {
    return '江苏省';
  }
  
  // 处理其他省份，确保省份名称格式正确
  const provinceMap = {
    '浙江': '浙江省',
    '广东': '广东省', 
    '山东': '山东省',
    '河南': '河南省',
    '四川': '四川省',
    '湖北': '湖北省',
    '湖南': '湖南省',
    '河北': '河北省',
    '安徽': '安徽省',
    '福建': '福建省',
    '江西': '江西省',
    '云南': '云南省',
    '贵州': '贵州省',
    '山西': '山西省',
    '陕西': '陕西省',
    '甘肃': '甘肃省',
    '青海': '青海省',
    '海南': '海南省',
    '辽宁': '辽宁省',
    '吉林': '吉林省',
    '黑龙江': '黑龙江省',
    '台湾': '台湾省'
  };
  
  // 检查是否在省份映射表中
  for (const [shortName, fullName] of Object.entries(provinceMap)) {
    if (trimmedName.includes(shortName)) {
      return fullName;
    }
  }
  
  // 处理直辖市和特别行政区
  const specialRegions = ['北京市', '上海市', '天津市', '重庆市', '香港特别行政区', '澳门特别行政区'];
  for (const region of specialRegions) {
    if (trimmedName.includes(region.replace('市', '').replace('特别行政区', ''))) {
      return region;
    }
  }
  
  // 处理自治区
  const autonomousRegions = {
    '内蒙古': '内蒙古自治区',
    '广西': '广西壮族自治区', 
    '西藏': '西藏自治区',
    '宁夏': '宁夏回族自治区',
    '新疆': '新疆维吾尔自治区'
  };
  
  for (const [shortName, fullName] of Object.entries(autonomousRegions)) {
    if (trimmedName.includes(shortName)) {
      return fullName;
    }
  }
  
  // 如果都不匹配，返回原始名称
  return trimmedName;
};

  /**
   * 返回首页 - 完全重置到业务展示页面的初始状态
   * 这是用户希望的"首页"状态：地球模式 + 左右数据展示
   */
  const backHome = () => {
    console.log('项目详情页面返回首页 - 完全重置状态');
    
    // 清除所有相关状态数据，确保返回到最初的页面状态
    localStorage.removeItem("isChina");
    localStorage.removeItem("clickProject");
    localStorage.removeItem("btnsActive");
    sessionStorage.removeItem("countryLevel");
    sessionStorage.removeItem("countryTitle");
    sessionStorage.removeItem("clickCountry");
    sessionStorage.removeItem("lastProjectState");
    sessionStorage.removeItem("clickProject");
    sessionStorage.removeItem("shouldResetToInitial");
    
    // 设置返回首页标记，确保业务展示页面恢复到初始状态
    sessionStorage.setItem("shouldResetToInitial", "true");
    sessionStorage.setItem("returnType", "backHome");
    
    // 跳转到业务布局首页
    router.replace({ path: "/business-display" });
  };

  /**
   * 处理缩略图点击切换
   * @param {number} index - 被点击的媒体项目索引
   * @param {Object} mediaItem - 被点击的媒体项目对象
   */
  const handleThumbnailClick = (index, mediaItem) => {
    // 先清除右侧容器内的所有内容
    const rightImageContainer = document.querySelector('.right-image-container .image-display-area');
    if (rightImageContainer) {
      rightImageContainer.innerHTML = '';
    }
    
    if (mediaItem.type === 'image') {
      // 图片处理
      currentImageIndex.value = mediaItem.index;
      currentMediaIndex.value = index;
      currentMediaType.value = 'image';
      
      // 创建并添加图片元素
      if (rightImageContainer) {
        const imgElement = document.createElement('img');
        imgElement.src = mediaItem.url;
        imgElement.alt = '项目图片';
        imgElement.style.width = '100%';
        imgElement.style.height = '100%';
        imgElement.style.objectFit = 'contain';
        rightImageContainer.appendChild(imgElement);
      }
    } else if (mediaItem.type === 'video') {
      // 视频处理
      currentMediaIndex.value = index;
      currentMediaType.value = 'video';
      
      // 创建视频元素
      if (rightImageContainer) {
        const videoElement = document.createElement('video');
        videoElement.src = mediaItem.url;
        videoElement.controls = true;
        videoElement.autoplay = true;
        videoElement.loop = true;
        videoElement.muted = false;
        videoElement.style.width = '100%';
        videoElement.style.height = '100%';
        videoElement.style.objectFit = 'contain';
        
        rightImageContainer.appendChild(videoElement);
      }
    }
  };


  </script>
  <style lang="scss" scoped>
.project-detail-containers {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #0a0f1a;
  z-index: 2;
}
  .content {
    width: 100%;
    height: 100%;
    pointer-events: all;
    position: relative;
    z-index: 1000;
  
    .bg {
      width: 100%;
      height: 150px;
      position: absolute;
      top: -28px;
      left: 0;
      background: url("@/assets/images/header/headerBgFont.png") no-repeat;
      background-size: 100% 100%;
      pointer-events: none;
      z-index: 30;
    }
  
    .topBg {
      width: 100%;
      height: 240px;
      position: absolute;
      top: 0;
      right: 0;
      background: url("@/assets/images/yingdi/top.png") no-repeat;
      background-size: 100% 100%;
      pointer-events: none;
      z-index: 2;
    }
  
    .bottomBg {
      width: 100%;
      height: 160px;
      position: absolute;
      left: 0;
      bottom: 0;
      // background: url("@/assets/images/yingdi/bottom.png") no-repeat;
      background-size: 100% 100%;
      pointer-events: none;
      z-index: 2;
    }
  
    .btns {
      width: 36px;
      height: 500px;
      position: absolute;
      top: 115px;
      left: 5px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      z-index: 6;
  
      >div {
        width: 36px;
        height: 160px;
        background: url("@/assets/images/project/btn.png") no-repeat;
        background-size: 100% 100%;
        color: #97acb4;
        box-sizing: border-box;
        padding: 0 5px;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        cursor: pointer;
  
        &.active {
          background: url("@/assets/images/project/btnActive.png") no-repeat;
          background-size: 100% 100%;
          color: #fff;
        }
        
        &.disabled {
          opacity: 0.5;
          cursor: not-allowed;
          filter: grayscale(0.8);
          pointer-events: none;
        }
      }
    }
  
    // 主要内容区域
    .main-content {
      position: absolute;
      top: 70px;
      left: 50px;
      right: 20px; // 右边留出基本间距
      bottom: 50px;
      display: flex;
      gap: 20px;
      z-index: 1;
    }

    // 左边内容展示区域 (3/10)
    .left-content-container {
      flex: 3.1; // 占据3/10的宽度
      min-width: 280px;
      height: 100%;
      display: flex;
      flex-direction: column;
      gap: 20px;
      overflow-y: auto;
      padding-right: 10px; /* Add some padding to separate from right section */

      /* Hide scrollbar for IE, Edge and Firefox */
      -ms-overflow-style: none;  /* IE and Edge */
      scrollbar-width: none;  /* Firefox */
      /* Hide scrollbar for Chrome, Safari and Opera */
      &::-webkit-scrollbar {
        display: none;
      }
    }

    // 右边图片展示区域 (7/10)
    .right-image-section {
      flex: 7; // 占据7/10的宽度
      display: flex;
      height: 100%;
      position: relative;
      gap: 20px; // 主图片和缩略图之间的间距
    }

    // 右边主要图片展示区域
    .right-image-container {
      flex: 1; // 占据右边区域的大部分
      height: 100%;
      
      .image-display-area {
        width: 100%;
        height: 100%;
        // border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        overflow: hidden;
        background: rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(5px);
        display: flex;
        align-items: center;
        justify-content: center;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
          transition: all 0.3s ease;
        }
        
        .no-image-placeholder {
          color: rgba(255, 255, 255, 0.5);
          font-size: 18px;
          text-align: center;
        }
      }
    }

    // 缩略图容器样式 - 在右边区域内
    .thumbnails-container {
      width: 320px;
      height: 100%;
      display: flex;
      flex-direction: column;
      gap: 12px;
      z-index: 8;
      pointer-events: auto;
      overflow-y: auto;
      flex-shrink: 0; // 防止缩略图区域被压缩
      padding-right: 8px; // 为了更好的视觉效果
      padding-top: 180px;
      
      // 隐藏滚动条但保留滚动功能
      scrollbar-width: none;
      -ms-overflow-style: none;
      &::-webkit-scrollbar {
        display: none;
      }
      
      // 添加滚动提示效果
      &:before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 2px;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
      }
      
      &:hover:before {
        opacity: 1;
      }
    }
  }
  .back-button {
      position: absolute;
      top: 25px;
      left: 170px;
      display: flex;
      align-items: center;
      gap: 8px;
      pointer-events: auto;
      cursor: pointer;
      z-index: 10;
      transition: all 0.3s ease;
      font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
      font-weight: normal;
      font-size: 16px;
      text-shadow: 0px 0px 4px #0085FF;
      text-align: right;
      font-style: normal;
      text-transform: none;
  }
  .backHome-button {
      position: absolute;
      top: 25px;
      left: 60px;
      display: flex;
      align-items: center;
      gap: 8px;
      pointer-events: auto;
      cursor: pointer;
      z-index: 10;
      transition: all 0.3s ease;
      font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
      font-weight: normal;
      font-size: 16px;
      text-shadow: 0px 0px 4px #0085FF;
      text-align: right;
      font-style: normal;
      text-transform: none;
      img{
        width: 18px;
        height: 18px;
      }
  }
  
  // 缩略图样式
  .thumbnail {
    width: 100%;
    height: 160px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    // overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    position: relative;
    
    &:hover {
      border-color: rgba(0, 89, 255, 0.6);
      transform: scale(1.02);
      box-shadow: 0 4px 12px rgba(0, 89, 255, 0.3);
    }
    
    &.active {
      border-color: #0080ff;
      box-shadow: 0 0 15px rgba(0, 47, 255, 0.6);
      transform: scale(1.05);
    }
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: opacity 0.3s ease;
      
      &:hover {
        opacity: 0.9;
      }
    }
    
    // 视频缩略图容器
    .video-thumbnail-container {
      position: relative;
      width: 100%;
      height: 100%;
      overflow: hidden;
      
      .video-thumbnail {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: opacity 0.3s ease;
        
        &:hover {
          opacity: 0.9;
        }
      }
      
      .video-play-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 48px;
        height: 48px;
        background: rgba(0, 0, 0, 0.7);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        transition: all 0.3s ease;
        
        &:hover {
          background: rgba(0, 89, 255, 0.8);
          transform: translate(-50%, -50%) scale(1.1);
        }
        
        svg {
          margin-left: 2px; // 稍微向右偏移，让播放图标更居中
        }
      }
    }
  }

/* 人员弹窗样式 */
.personnel-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(8px);
  pointer-events: auto;
}

.personnel-modal {
  position: relative;
  width: 3300px;
  max-width: 90%;
  height: 90%;
  overflow: hidden;
  border-radius: 16px;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.7);
  border: 2px solid rgba(77, 151, 255, 0.4);
}

.modal-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.modal-background .modal-bg-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 14px;
}

.modal-content {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  padding: 40px;
  display: flex;
  flex-direction: column;
  background: rgba(5, 15, 35, 0.1);
  backdrop-filter: blur(2px);
}

.modal-header {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid rgba(77, 151, 255, 0.3);
}

.modal-header .modal-title {
  font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
font-weight: 400;
font-size: 52px;
text-align: center;
font-style: normal;
text-transform: none;
}

.modal-header .close-btn {
  position: absolute;
  top: 0;
  right: 0;
  background: rgba(255, 75, 75, 0.2);
  border: 2px solid rgba(255, 75, 75, 0.5);
  color: #ff4b4b;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.modal-header .close-btn:hover {
  background: rgba(255, 75, 75, 0.4);
  border-color: rgba(255, 75, 75, 0.8);
  transform: scale(1.1);
  box-shadow: 0 4px 20px rgba(255, 75, 75, 0.3);
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding-right: 10px;
}

.modal-body::-webkit-scrollbar {
  width: 8px;
}

.modal-body::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb {
  background: rgba(77, 151, 255, 0.6);
  border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: rgba(77, 151, 255, 0.8);
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #ffffff;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(77, 151, 255, 0.3);
  border-top: 4px solid #4d97ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 表格容器样式 */
.personnel-table-container {
  width: 100%;
  overflow-x: auto;
  padding: 10px;
}

/* 表格样式 */
.personnel-table {
  width: 100%;
  border-collapse: collapse;
  background: rgba(15, 25, 45, 0.8);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(77, 151, 255, 0.3);
  backdrop-filter: blur(10px);
}

/* 表头样式 */
.personnel-table thead {
  background: rgba(77, 151, 255, 0.2);
}

.personnel-table th {
  padding: 20px 16px;
  text-align: center;
  color: #ffffff;
  font-weight: bold;
  font-size: 24px;
  border-bottom: 2px solid rgba(77, 151, 255, 0.4);
  white-space: nowrap;
  min-width: 140px;
}

/* 表格行样式 */
.personnel-table tbody tr {
  transition: all 0.3s ease;
}

.personnel-table tbody tr:hover {
  background: rgba(77, 151, 255, 0.1);
}

.personnel-table tbody tr:nth-child(even) {
  background: rgba(5, 15, 35, 0.3);
}

.personnel-table tbody tr:nth-child(even):hover {
  background: rgba(77, 151, 255, 0.15);
}

/* 表格单元格样式 */
.personnel-table td {
  padding: 18px 16px;
  text-align: center;
  color: #cccccc;
  font-size: 18px;
  border-bottom: 1px solid rgba(77, 151, 255, 0.1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

/* 状态徽章样式 */
.status-badge {
  padding: 8px 18px;
  border-radius: 20px;
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  display: inline-block;
}

.status-active {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid rgba(76, 175, 80, 0.4);
}

.status-inactive {
  background: rgba(244, 67, 54, 0.2);
  color: #F44336;
  border: 1px solid rgba(244, 67, 54, 0.4);
}

/* 照片缩略图样式 */
.photo-thumbnail {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  object-fit: cover;
  border: 2px solid rgba(77, 151, 255, 0.4);
  cursor: pointer;
  transition: all 0.3s ease;
}

.photo-thumbnail:hover {
  transform: scale(1.1);
  border-color: rgba(77, 151, 255, 0.8);
}

/* 二维码样式 */
.qr-code {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.qr-code:hover {
  transform: scale(1.1);
}

/* 无内容提示样式 */
.no-photo, .no-qr {
  color: #666666;
  font-style: italic;
}

/* 操作按钮样式 */
.action-btn {
  padding: 10px 20px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.view-btn {
  background: rgba(77, 151, 255, 0.2);
  color: #4d97ff;
  border: 1px solid rgba(77, 151, 255, 0.4);
}

.view-btn:hover {
  background: rgba(77, 151, 255, 0.3);
  border-color: rgba(77, 151, 255, 0.6);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(77, 151, 255, 0.2);
}

/* 暂无数据样式 */
.no-data {
  text-align: center;
  color: #888888;
  font-style: italic;
  padding: 40px 20px !important;
  font-size: 18px;
}

/* 分页组件样式 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 20px 0;
  border-top: 1px solid rgba(77, 151, 255, 0.2);
}

.pagination-info {
  color: #cccccc;
  font-size: 18px;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-btn {
  padding: 10px 16px;
  border: 1px solid rgba(77, 151, 255, 0.3);
  background: rgba(77, 151, 255, 0.1);
  color: #4d97ff;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  min-width: 80px;
}

.page-btn:hover:not(:disabled) {
  background: rgba(77, 151, 255, 0.2);
  border-color: rgba(77, 151, 255, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(77, 151, 255, 0.2);
}

.page-btn:disabled {
  background: rgba(128, 128, 128, 0.1);
  color: #666666;
  border-color: rgba(128, 128, 128, 0.2);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.page-numbers {
  display: flex;
  gap: 8px;
  margin: 0 12px;
}

.page-number {
  padding: 10px 14px;
  border: 1px solid rgba(77, 151, 255, 0.3);
  background: rgba(77, 151, 255, 0.1);
  color: #4d97ff;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  min-width: 44px;
  text-align: center;
}

.page-number:hover {
  background: rgba(77, 151, 255, 0.2);
  border-color: rgba(77, 151, 255, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(77, 151, 255, 0.2);
}

.page-number.active {
  background: rgba(77, 151, 255, 0.8);
  color: #ffffff;
  border-color: rgba(77, 151, 255, 1);
  box-shadow: 0 4px 16px rgba(77, 151, 255, 0.4);
  transform: translateY(-2px);
}

.page-number.active:hover {
  background: rgba(77, 151, 255, 0.9);
  transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .personnel-table th,
  .personnel-table td {
    padding: 14px 12px;
    font-size: 16px;
  }
  
  .personnel-table th {
    font-size: 20px;
  }
  
  .pagination-container {
    flex-direction: column;
    gap: 16px;
    align-items: center;
  }
  
  .pagination-controls {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .page-btn, .page-number {
    padding: 8px 12px;
    font-size: 14px;
    min-width: auto;
  }
  
  .action-btn {
    font-size: 14px;
    padding: 8px 16px;
  }
  
  .status-badge {
    font-size: 14px;
    padding: 6px 14px;
  }
}

/* 滚动条样式 */
.personnel-table-container::-webkit-scrollbar {
  height: 8px;
}

.personnel-table-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.personnel-table-container::-webkit-scrollbar-thumb {
  background: rgba(77, 151, 255, 0.6);
  border-radius: 4px;
}

.personnel-table-container::-webkit-scrollbar-thumb:hover {
  background: rgba(77, 151, 255, 0.8);
}

/* 人员详情列表弹窗样式 */
.personnel-detail-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(5px);
}

.personnel-detail-modal {
  position: relative;
  width: 3300px;
  max-width: 90%;
  height: 90%;
  overflow: hidden;
  border-radius: 16px;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.7);
  border: 2px solid rgba(77, 151, 255, 0.4);
}

.personnel-detail-modal .modal-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.personnel-detail-modal .modal-bg-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 14px;
}

.personnel-detail-modal .modal-content {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  padding: 40px;
  display: flex;
  flex-direction: column;
  background: rgba(5, 15, 35, 0.1);
  backdrop-filter: blur(2px);
}

.personnel-detail-modal .modal-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid rgba(77, 151, 255, 0.3);
  min-height: 60px;
  z-index: 100;
}

.personnel-detail-modal .back-to-list-button {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 8px;
  pointer-events: auto;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
  font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 16px;
  text-shadow: 0px 0px 4px #0085FF;
  text-align: right;
  font-style: normal;
  text-transform: none;
  color: #ffffff;
}

.personnel-detail-modal .back-to-list-button:hover {
  opacity: 0.8;
  transform: translateY(-50%) scale(1.05);
}

.personnel-detail-modal .back-to-list-button img {
  width: 18px;
  height: 18px;
}

.personnel-detail-modal .modal-title {
  font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
  font-weight: 400;
  font-size: 52px;
  text-align: center;
  font-style: normal;
  text-transform: none;
  color: #ffffff;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.8);
  margin: 0;
}

.personnel-detail-modal .close-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  background: rgba(255, 75, 75, 0.8);
  border: 2px solid rgba(255, 75, 75, 0.9);
  color: #ffffff;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  z-index: 999;
  pointer-events: auto;
  user-select: none;
}

.personnel-detail-modal .close-btn:hover {
  background: rgba(255, 75, 75, 1);
  border-color: rgba(255, 75, 75, 1);
  transform: scale(1.1);
  box-shadow: 0 4px 20px rgba(255, 75, 75, 0.5);
  color: #ffffff;
}

.personnel-detail-modal .modal-body {
  flex: 1;
  overflow-y: auto;
  padding-right: 10px;
}

.personnel-detail-modal .design-preview-section {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(77, 151, 255, 0.3);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 30px;
  backdrop-filter: blur(10px);
}

.personnel-detail-modal .section-title {
  font-size: 24px;
  font-weight: bold;
  color: #ffffff;
  margin: 0 0 20px 0;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.6);
  font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
}

.personnel-detail-modal .design-content {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 20px;
  min-height: 200px;
}

.personnel-detail-modal .person-detail-info {
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(10px);
}

.personnel-detail-modal .detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.personnel-detail-modal .detail-item {
  background: rgba(255, 255, 255, 0.08);
  border: 2px solid rgba(77, 151, 255, 0.25);
  border-radius: 12px;
  padding: 28px 24px;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
  margin-bottom: 12px;
  min-height: 80px;
}

.personnel-detail-modal .detail-item:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(77, 151, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(77, 151, 255, 0.2);
}

.personnel-detail-modal .detail-item .label {
  font-size: 22px;
  font-weight: bold;
  color: #4d97ff;
  min-width: 150px;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.8);
  font-family: 'YouSheBiaoTiHei', 'Microsoft YaHei', sans-serif;
}

.personnel-detail-modal .detail-item .value {
  font-size: 20px;
  color: #ffffff;
  flex: 1;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.8);
  font-family: 'Alibaba PuHuiTi 2.0', 'Microsoft YaHei', sans-serif;
  font-weight: 500;
}

.personnel-detail-modal .photo-item,
.personnel-detail-modal .qr-item {
  grid-column: span 2;
  flex-direction: column;
  align-items: flex-start;
}

.personnel-detail-modal .photo-container,
.personnel-detail-modal .qr-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 12px;
}

.personnel-detail-modal .detail-photo,
.personnel-detail-modal .detail-qr {
  max-width: 200px;
  max-height: 200px;
  border-radius: 12px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.personnel-detail-modal .detail-photo:hover,
.personnel-detail-modal .detail-qr:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 24px rgba(77, 151, 255, 0.3);
}

.personnel-detail-modal .no-photo,
.personnel-detail-modal .no-qr {
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
  font-size: 16px;
}

/* 滚动条样式 */
.personnel-detail-modal .modal-body::-webkit-scrollbar {
  width: 8px;
}

.personnel-detail-modal .modal-body::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.personnel-detail-modal .modal-body::-webkit-scrollbar-thumb {
  background: rgba(77, 151, 255, 0.6);
  border-radius: 4px;
}

.personnel-detail-modal .modal-body::-webkit-scrollbar-thumb:hover {
  background: rgba(77, 151, 255, 0.8);
}

/* 未选择人员时的样式 */
.no-person-selected {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 18px;
  font-style: italic;
}

/* 8个标签页导航样式 */
.personnel-tabs-navigation {
  display: flex;
  gap: 8px;
  margin-bottom: 30px;
  padding: 0 20px;
  width: 100%;
  box-sizing: border-box;
  position: relative;
  z-index: 20;
  pointer-events: auto;
}

.tab-item {
  flex: 1;
  height: 60px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  z-index: 21;
  pointer-events: auto;
}

.tab-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(77, 151, 255, 0.3);
}

.tab-item.active {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(77, 151, 255, 0.5);
}

.tab-block {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(77, 151, 255, 0.2), rgba(77, 151, 255, 0.1));
  border: 2px solid rgba(77, 151, 255, 0.3);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.tab-item:hover .tab-block {
  background: linear-gradient(135deg, rgba(77, 151, 255, 0.3), rgba(77, 151, 255, 0.2));
  border-color: rgba(77, 151, 255, 0.5);
}

.tab-item.active .tab-block {
  background: linear-gradient(135deg, rgba(77, 151, 255, 0.8), rgba(77, 151, 255, 0.6));
  border-color: rgba(77, 151, 255, 1);
  box-shadow: inset 0 2px 10px rgba(77, 151, 255, 0.3);
}

.tab-name {
  font-size: 18px;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  text-align: center;
  line-height: 1.2;
  padding: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tab-item.active .tab-name {
  color: #ffffff;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.8);
}

/* 标签页内容区域样式 */
.personnel-tabs-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  border-radius: 12px;
  position: relative;
  z-index: 15;
  pointer-events: auto;
  background-image: url('@/assets/images/project/detailBg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

/* 信息展示网格样式 */
.info-display-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16px;
  margin-top: 20px;
}

.info-item {
  background: transparent;
  border: none;
  border-radius: 0;
  padding: 12px 8px;
  display: flex;
  align-items: flex-start;
  text-align: left;
  gap: 12px;
  transition: all 0.3s ease;
  margin-bottom: 8px;
  min-height: 100px;
}

.info-item:hover {
  transform: translateY(-1px);
}

.info-item .label {
  font-size: 14px;
  font-weight: bold;
  color: #4d97ff;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.8);
  font-family: 'YouSheBiaoTiHei', 'Microsoft YaHei', sans-serif;
  line-height: 1.2;
}

.info-item .value {
  font-size: 13px;
  color: #ffffff;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.8);
  word-break: break-all;
  font-family: 'Alibaba PuHuiTi 2.0', 'Microsoft YaHei', sans-serif;
  font-weight: 400;
  line-height: 1.4;
  opacity: 0.9;
}

/* 信息项图标样式 */
.info-item .info-icon {
  width: 120px;
  height: 80px;
  flex-shrink: 0;
  margin-top: 2px;
}

/* 信息文本容器样式 */
.info-item .info-text {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 4px;
}

.info-item.span-2 {
  grid-column: span 3;
}

/* 照片值样式 */
.photo-value {
  display: flex;
  align-items: center;
}

.info-detail-photo {
  max-width: 80px;
  max-height: 80px;
  border-radius: 6px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  display: block;
}

.info-detail-photo:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(77, 151, 255, 0.3);
}

.no-photo-text {
  color: #999;
  font-size: 12px;
  font-style: italic;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.8);
}

/* 照片项目网格对齐样式 - 继承父级网格的单列宽度，让每个照片项目正常占用一个网格列 */

/* 保留原有的photo-row样式，以防其他地方还在使用 */
.photos-row {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16px;
  margin-top: 20px;
}

.info-item.photo-item {
  grid-column: span 3;
  min-height: 200px;
}

.photo-value {
  display: block;
  margin-top: 8px;
}

.photo-value .detail-photo {
  max-width: 160px;
  max-height: 160px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  display: block;
}

.photo-value .detail-photo:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 20px rgba(77, 151, 255, 0.3);
}

.photo-value .no-photo {
  color: #999;
  font-size: 14px;
  font-style: italic;
}

/* 保留原有的photo-section样式，以防其他地方还在使用 */
.info-item.photo-section {
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-height: 200px;
  grid-column: span 3;
  background: transparent;
  border: none;
}

.info-item.photo-section .photo-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 12px;
}

.info-item.photo-section .detail-photo {
  max-width: 160px;
  max-height: 160px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.info-item.photo-section .detail-photo:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 20px rgba(77, 151, 255, 0.3);
}

/* 资格证书样式 */
.certificate-section {
  margin-bottom: 24px;
}

.certificate-divider {
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(77, 151, 255, 0.3), transparent);
  margin: 24px 0;
}

.cert-info.cert-attachments {
  grid-column: span 2;
  flex-direction: column;
  align-items: flex-start;
}

.attachment-list {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-top: 8px;
}

.cert-attachment {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 8px;
  border: 2px solid rgba(255, 255, 255, 0.4);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.cert-attachment:hover {
  transform: scale(1.1);
  border-color: rgba(77, 151, 255, 0.6);
  box-shadow: 0 4px 12px rgba(77, 151, 255, 0.3);
}

/* 数据表格样式 */
.data-table-container {
  width: 100%;
  overflow-x: auto;
  padding: 10px;
}

/* 数据表格容器滚动条样式 */
.data-table-container::-webkit-scrollbar {
  height: 8px;
}

.data-table-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.data-table-container::-webkit-scrollbar-thumb {
  background: rgba(77, 151, 255, 0.6);
  border-radius: 4px;
}

.data-table-container::-webkit-scrollbar-thumb:hover {
  background: rgba(77, 151, 255, 0.8);
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  background: rgba(15, 25, 45, 0.8);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(77, 151, 255, 0.3);
  backdrop-filter: blur(10px);
}

.data-table thead {
  background: rgba(77, 151, 255, 0.2);
}

.data-table th {
  padding: 20px 16px;
  text-align: center;
  color: #ffffff;
  font-weight: bold;
  font-size: 24px;
  border-bottom: 2px solid rgba(77, 151, 255, 0.4);
  white-space: nowrap;
  min-width: 140px;
}

.data-table tbody tr {
  transition: all 0.3s ease;
}

.data-table tbody tr:hover {
  background: rgba(77, 151, 255, 0.1);
}

.data-table tbody tr:nth-child(even) {
  background: rgba(5, 15, 35, 0.3);
}

.data-table tbody tr:nth-child(even):hover {
  background: rgba(77, 151, 255, 0.15);
}

.data-table td {
  padding: 18px 16px;
  text-align: center;
  color: #cccccc;
  font-size: 18px;
  border-bottom: 1px solid rgba(77, 151, 255, 0.1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

/* 评价信息图片样式 */
.evaluation-pics {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
}

.evaluation-pic {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: 8px;
  border: 2px solid rgba(255, 255, 255, 0.4);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.evaluation-pic:hover {
  transform: scale(1.2);
  border-color: rgba(77, 151, 255, 0.6);
}

/* 附件链接样式 */
.attachment-link a {
  color: #4d97ff;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.3s ease;
}

.attachment-link a:hover {
  color: #66a3ff;
  text-decoration: underline;
}

/* 状态徽章样式 */
.status-reward {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid rgba(76, 175, 80, 0.4);
}

.status-penalty {
  background: rgba(244, 67, 54, 0.2);
  color: #F44336;
  border: 1px solid rgba(244, 67, 54, 0.4);
}

.status-qualified {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid rgba(76, 175, 80, 0.4);
}

.status-unqualified {
  background: rgba(244, 67, 54, 0.2);
  color: #F44336;
  border: 1px solid rgba(244, 67, 54, 0.4);
}

.no-attachment, .no-pic {
  color: #666666;
  font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .personnel-detail-modal {
    width: 95%;
    height: 90%;
  }
  
  .personnel-detail-modal .modal-content {
    padding: 20px;
  }
  
  .personnel-detail-modal .modal-title {
    font-size: 24px;
  }
  
  .personnel-detail-modal .section-title {
    font-size: 20px;
  }
  
  .personnel-detail-modal .detail-grid {
    grid-template-columns: 1fr;
  }
  
  .personnel-detail-modal .photo-item,
  .personnel-detail-modal .qr-item {
    grid-column: span 1;
  }
  
  .personnel-detail-modal .back-to-list-button {
    font-size: 14px;
  }
  
  .personnel-detail-modal .back-to-list-button img {
    width: 16px;
    height: 16px;
  }
  
  /* 小屏幕下的标签页样式 */
  .personnel-tabs-navigation {
    padding: 0 10px;
    gap: 4px;
  }
  
  .tab-item {
    height: 50px;
  }
  
  /* 移动端图片展示样式调整 */
  .info-detail-photo {
    max-width: 60px;
    max-height: 60px;
  }
  
  /* 移动端照片项目样式 - 保持网格布局，继承默认网格样式即可 */
  
  .tab-name {
    font-size: 14px;
    padding: 4px;
  }
}

/* 超小屏幕（手机横屏）适配 */
@media (max-width: 480px) {
  .personnel-tabs-navigation {
    flex-wrap: wrap;
    gap: 6px;
  }
  
  .tab-item {
    flex: 1 1 calc(25% - 6px);
    min-width: calc(25% - 6px);
    height: 45px;
  }
  
  .tab-name {
    font-size: 12px;
    padding: 2px;
  }
}
</style>
  