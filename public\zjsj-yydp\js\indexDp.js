$(function(){
    initRender();
    // httpAjaxPost("/admin-api/system/auth/login",{"username":"admin","password":"Admin258369"},function(res){
    //     console.log(res,"-------------")
    //     render.token = res.data.accessToken;
    //     httpAjax("/admin-api/globalManage/zjmanage/largescreen/getForeignInfoEarth",function(res){
    //         console.log(res,"---------");
    //     });
    // })
    render.language = "cn";
    window.addEventListener('message', function (e) {
        var data = e.data;
        console.log(data,"------------")
        if(data.eve=="cancle"){     //返回上一级 
            outCountry();
        }else if(data.eve=="changeModel"){     //切换模式
            if(data.model) window.parent.postMessage({eve:"earth"},"*");
            changeModel(data.data);
        }else if(data.type=="token"){     //获取token
            // initEarth();
            if(render.token){
                render.token = data.data;
            }else{
                render.token = data.data;
                getForeignInfoByName("earth","",function(){
                    showCountryMeshs("earth");
                })
            }
        }else if(data.eve=="changeLanguage"){     //切换语言
            changeLanguage(data.data);
        }else if(data.eve=="highLigtDemo"){     //项目高亮
            highLigtDemo(data.name);
        }else if(data.eve=="changeDemoInfoByProjectType"){     //根据项目类型切换
            changeDemoInfoByProjectType(data.type);
        }
    })
    // var vConsole = new VConsole();
    render.touchePoint = {x:0,y:0};
    let map = new THREE.TextureLoader().load(creatArrowImg(136,61,30,"rgba(0,0,0,0)","#fff") || "img/arrow.png");
    map.wrapS = map.wrapT = THREE.RepeatWrapping;
    map.repeat.x = 30;
    render.hilightPathMaterial = new THREE.MeshBasicMaterial({ color: 0xfece61, map: map,transparent:true,depthTest:false,depthWrite:false });
})
function isMobile() {
    const mobileRegEx = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
    return mobileRegEx.test(navigator.userAgent);
}
function touchePointEvent(){
    if(event){
        render.touchePoint = {x:event.touches[0].clientX,y:event.touches[0].clientY};
    }
}
let render = null;
function initRender() {
    render = new ZTRender("map");
    render.mapHeight = 300;         //地图高度
    render.isPhone = isMobile();
    //物理灯光
    render.render.physicallyCorrectLights = true;
    // render.render.shadowMap.autoUpdate = false;         //启用场景中的阴影自动更新
    let hei = new THREE.HemisphereLight(0xffffff, 0x444444, 0);
    render.heiLight = hei;
    render.scene.add(hei);
    // const light = new THREE.DirectionalLight(0xffffff, 0.5);
    // render.scene.add(light);
    // light.position.set(-10000,60000,10000)
    // // light.position.set(2000, 2000, -1200); //default; light shining from top
    // // light.castShadow = true; // default false
    // // light.shadow.camera.near = 1;
    // // light.shadow.camera.far = 3500;
    // // light.shadow.camera.right = 1500;
    // // light.shadow.camera.left = - 1500;
    // // light.shadow.camera.top = 1500;
    // // light.shadow.camera.bottom = - 1500;
    // // light.shadow.mapSize.width = 1024;
    // // light.shadow.mapSize.height = 1024;
    // // light.shadowCameraNear = 1;
    // // light.shadow.bias = -0.0000001;
    // render.light = light;
    // render.control.object.position.set(0, 1000, 1000);
    render.control.maxPolarAngle = THREE.MathUtils.degToRad(95);
    render.control.minPolarAngle = THREE.MathUtils.degToRad(70);
    // render.control.panTarget.set(-94.46866670200413,2.000000000000003, -59.76570390048769);
    // render.control.screenSpacePanning = true;
    render.stats = new Stats();
    render.stats.domElement.style.zIndex = 100;
    // document.body.appendChild(render.stats.domElement);
    render.setView({target:{x:0,y:800,z:0},position:{x:17258.775269189373,y:3328.6396507272502,z:-8926.529598513758}});
    // render.setView({target:{x:370.687270800479,y:0,z:0},position:{x:-6175.727402025576,y:3627.838042401806,z:-24026.11265694585}})
    render.events = new ZTMapEvents(render.scene, render.camera, render.container);

    render.update = function () {
        render.stats.update();
        if(render.lightGroups)render.lightGroups.rotation.y = render.control.getAzimuthalAngle();
        if(render.planMesh)render.planMesh.rotation.y = render.control.getAzimuthalAngle();
        if(render.earthInnerObject) render.earthInnerObject.rotation.y = render.control.getAzimuthalAngle() - THREE.MathUtils.degToRad(90);
        if(render.earthDxqObject) render.earthDxqObject.rotation.y = render.control.getAzimuthalAngle() - THREE.MathUtils.degToRad(90);
    }
    render.scene.children[1].intensity = 0;
    render.animationsTool = new Animations();
    // render.labelRenderer.enableAvoid = true;
    render.control.enableDamping = true;
    render.control.dampingFactor = 0.05;
    render.control.zoomSpeed = 3;
    render.control.mouseButtons = { LEFT: THREE.MOUSE.ROTATE, MIDDLE: THREE.MOUSE.DOLLY, RIGHT: THREE.MOUSE.PAN };
    render.control.touches = { ONE: THREE.TOUCH.ROTATE, TWO: THREE.TOUCH.DOLLY_PAN };
    render.control.autoRotateSpeed = -0.5;
    
    // render.control.minAzimuthAngle = THREE.MathUtils.degToRad(85);
    render.control.minAzimuthAngle = THREE.MathUtils.degToRad(0);
    render.control.maxAzimuthAngle = THREE.MathUtils.degToRad(-95);
    initCubeTexture(render,function(){
    })
    initEarth();
    window.parent.postMessage({eve:"loadOk"},"*");
    initLights();
    render.setTargetFPS(60);
}
function initLights(){
    render.lightGroups = new THREE.Group();
    render.scene.add(render.lightGroups);
    // let pointLight = new THREE.PointLight({color:0xffffff});
    // render.pointLight = pointLight;
    // render.lightGroups.add(render.pointLight);
    // render.pointLight.intensity = 70000;
    // render.pointLight.decay = 1.05;
    // render.pointLight.distance = 100000;
    // render.pointLight.position.set(7698.521564563551, 2925.6269801880658,517.6721715458398);
    // let pointLight1 = pointLight.clone();
    // pointLight1.position.set(-7698.521564563551, 2925.6269801880658,200);
    const light = new THREE.DirectionalLight(0xffffff, 5);
    render.scene.add(light);
    light.position.set(10000,10000,1000);
    // light.castShadow = true; // default false
    // light.shadow.camera.near = 1;
    // light.shadow.camera.far = 3500;
    // light.shadow.camera.right = 1500;
    // light.shadow.camera.left = - 1500;
    // light.shadow.camera.top = 1500;
    // light.shadow.camera.bottom = - 1500;
    // light.shadow.mapSize.width = 4096;
    // light.shadow.mapSize.height = 4096;
    // light.shadowCameraNear = 1;
    // light.shadow.bias = -0.0000001;
    render.light = light;
    render.lightGroups.add(render.light);

}
function initCubeTexture(render,callback){
    var path = "sky/sky/";
    var format = '.jpg';
    var urls = [
        "px"+ format, "nx"+ format,
        "py"+ format, "ny"+ format,
        "pz"+ format, "nz"+ format
    ];
    // var vConsole = new VConsole();
    const loader = new THREE.CubeTextureLoader();
    loader.setPath( path );
    loader.load( urls,function(texture){
        render.envMap = texture;
        // console.log('Hello world',render.envMap,texture);
        // render.envMap.mapping = THREE.CubeReflectionMapping;
    
        // const textureLoader = new THREE.TextureLoader();
        // render.envMap = textureLoader.load('textures/nx.hdr');
        // render.envMap.mapping = THREE.EquirectangularReflectionMapping;
        // render.envMap.encoding = THREE.sRGBEncoding;
        // render.render.gammaOutput = true;
        render.scene.environment = render.envMap;
        if(callback) callback();
        // render.scene.background = render.envMap;
    } );
}
// 创建地球
function initEarth(){
    // render.earthSize = 6371000;
    render.earthSize = 6371;
    let texture = new THREE.TextureLoader().load("textures/earth4.jpg");
    render.earthMaterial = new THREE.MeshStandardMaterial({color:0xffffff,transparent:true,opacity:1,metalness:0,roughness:0.5,depthWrite:false});  //
    render.planMapMaterial = new THREE.MeshStandardMaterial({color:0xffffff,transparent:true,opacity:1,metalness:0,roughness:0,depthWrite:false});  //
    let sphereGeometry = new THREE.SphereGeometry(render.earthSize, 64, 64);
    render.earthObject = new THREE.Mesh(sphereGeometry, render.earthMaterial);
    render.earthObject.renderOrder = 2;
    render.earthObject.castShadow = true;
    render.earthObject.receiveShadow = true;
    render.scene.add(render.earthObject);
    render.earthObject.currModel = "earth";
    render.control.enablePan = false;
    render.control.minDistance = 15000;
    render.control.maxDistance = 50000;
    let sphereGeometry1 = new THREE.SphereGeometry(render.earthSize + 1000, 12, 12);
    render.sphereLineMaterial = new THREE.MeshStandardMaterial({color:0x0b4288,transparent:true,opacity:0.1,wireframe:true,depthWrite:false,blending:2,roughness:0,metalness:0});  //
    render.sphereLineObject = new THREE.Mesh(sphereGeometry1, render.sphereLineMaterial);
    render.sphereLineObject.scale.y = 0.93;
    // render.scene.add(render.sphereLineObject);
    render.sphereLineObject.renderOrder = 3;
    // test();
    initWorldJSON();
    initStars(sphereGeometry1);

    let sphereGeometry2 = new THREE.SphereGeometry(render.earthSize - 10, 64, 64);
    let earthInnerTexture = createEarthBg();
    earthInnerTexture.offset.y = -0.05;
    render.earthInnerMaterial = new THREE.MeshStandardMaterial({color:0xffffff,transparent:true,opacity:1,roughness:0.7,metalness:0.5,map:earthInnerTexture,envMapIntensity:1});  //
    render.earthInnerObject = new THREE.Mesh(sphereGeometry2, render.earthInnerMaterial);
    render.earthInnerObject.renderOrder = 1;
    render.scene.add(render.earthInnerObject);
    render.earthInnerObject.castShadow = true;
    render.earthInnerObject.receiveShadow = true;

    // let bloomTexture = createEarthBloomBg();
    // bloomTexture.offset.y = -0.05;
    // const innerSphereGeometry = new THREE.SphereGeometry(render.earthSize + 100, 64, 64); // 半径略小于外层球体
    // const innerMaterial = new THREE.MeshStandardMaterial({ color: 0xffffff, opacity: 0.5, transparent: true,side:1,map:bloomTexture });
    // render.earthDxqObject = new THREE.Mesh(innerSphereGeometry, innerMaterial);
    // // render.earthDxqObject.renderOrder = 1;
    // render.scene.add(render.earthDxqObject);

    // 创建大气层
    const atmosphereGeometry = new THREE.SphereGeometry(render.earthSize + 600, 64, 64);
      
    // 大气层材质（自定义着色器）
    const atmosphereMaterial = new THREE.ShaderMaterial({
        uniforms:{
            uColor: { value: new THREE.Color(0xa1e4f7) }, // 大气层的颜色
            bloomIntensity: { value: 1.0 }, // 大气层的发光强度
            opacity: { value: 0.2 }, // 大气层的透明度
        },
      vertexShader: `
        varying vec3 vNormal;
        void main() {
          vNormal = normalize(normalMatrix * normal);
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        varying vec3 vNormal;
        uniform vec3 uColor;
        uniform float bloomIntensity;
        uniform float opacity;
        void main() {
          float intensity = pow(0.8 - dot(vNormal, vec3(0, 0, 1)), 3.0);
          gl_FragColor = vec4(uColor, opacity) * intensity * bloomIntensity;
        }
      `,
      side: THREE.BackSide,
      blending: THREE.AdditiveBlending,
      transparent: true
    });
    
    render.earthDxqObject = new THREE.Mesh(atmosphereGeometry, atmosphereMaterial);
    render.scene.add(render.earthDxqObject);
    render.earthDxqObject.castShadow = true;
    render.earthDxqObject.receiveShadow = true;


    let planGeometry = new THREE.BoxBufferGeometry(1,1,1);
    let material = new THREE.MeshStandardMaterial({color:0xffffff,side:0,transparent:true,opacity:0,depthWrite:false});
    render.planMesh = new THREE.Mesh(planGeometry,material);
    render.planMesh.scale.set(20000,10000,7000);
    render.scene.add(render.planMesh);
    // let earthInnerObjectClone = render.earthInnerObject.clone();
    // earthInnerObjectClone.material = new THREE.MeshStandardMaterial({color:0xffffff,transparent:true,opacity:1,roughness:0,metalness:0.2,map:earthInnerTexture,envMapIntensity:0.8});  //
    // render.earthInnerObject.add(earthInnerObjectClone);
    
    render.control.autoRotate = true;
    render.events.on("hover",render.earthInnerObject,function(){
        render.control.autoRotate = false;
    },function(){
        if(render.earthObject.currModel=="earth")render.control.autoRotate = true;
    })

    render.currCountrysModel = new THREE.Group();
    render.currCountrysModel.scale.y = render.mapHeight;
    render.scene.add(render.currCountrysModel);
    // render.labelRenderer.enableIntersect = true;
    render.currCountrysModel.level = "earth";

    // getForeignInfoByName("earth","",function(){
    //     showCountryMeshs("earth");
    // })

    render.currDemoInfosModel = new THREE.Group();
    render.scene.add(render.currDemoInfosModel);
    window.parent.postMessage({eve:"clickTitle",title:"earth"},"*");
}
// 经纬度球面坐标
function lonlatToEarthVector3(longitude, latitude,radius) {
    var lg = THREE.MathUtils.degToRad(90 - longitude);// - THREE.MathUtils.degToRad(110);
    var lt = THREE.MathUtils.degToRad(latitude);
    var temp = radius * Math.cos(lt);
    var x = temp * Math.sin(lg);
    var y = radius * Math.sin(lt);
    var z = temp * Math.cos(lg);
    // console.log(new THREE.Spherical( radius, lg, lt ));
    return {
        x: x,
        y: y,
        z: -z
    }
}
// 经纬度平面坐标
function lonlatToPlanVector3(longitude, latitude,radius) {
    var theta = THREE.MathUtils.degToRad(longitude);
    var phi = THREE.MathUtils.degToRad(latitude);
    // var temp = render.earthSize * Math.cos(lt);
    // var x = temp * Math.sin(lg);
    // var y = render.earthSize * Math.sin(lt);
    // var z = temp * Math.cos(lg);

    // 将球面坐标投影到平面
    // const theta = Math.atan2(z, x);
    // const phi = Math.asin(y / radius);
    // 计算平面坐标
    const u = theta / (2 * Math.PI) + 0.5;
    const v = phi / Math.PI + 0.5;
    let w_ = 2 * Math.PI * radius;
    let h_ = w_ / 2;
    let x = u * w_ - h_;
    let z = v * h_ - (h_ / 2);
    return {
        x: -x,
        y: 0,
        z: z
    }
    // let h_ = w_ / 1.5;
    // let x = u * w_  - (h_ / 2);
    // let z = v * h_ - (h_ / 2);
    // return {
    //     x: -x,
    //     y: 0,
    //     z: z
    // }
}
/**
 * 切换地图模式
 * @param {*} model  plan:平面，earth:地球
 * @returns 
 */
function changeModel(model,callback,isNotHttp){
    if(["plan","earth"].indexOf(model)==-1) return;
    if(render.earthObject.currModel == model) return;
    render.earthObject.currModel = model;
    clearModels();
    if(!render.earthObject.planPositions){
        let sphereGeometry = render.earthObject.geometry;
        const spherePositions = sphereGeometry.getAttribute('position');
        const positions = [],earthPositions = [];
        const r = render.earthSize;
        for (let i = 0; i < spherePositions.count; i++) {
            const x = spherePositions.getX(i);
            const y = spherePositions.getY(i);
            const z = spherePositions.getZ(i);
            earthPositions.push(x,y,z);
            // 将球面坐标投影到平面
            const theta = Math.atan2(z, x);
            const phi = Math.asin(y / r);
            // 计算平面坐标
            const u = theta / (2 * Math.PI) + 0.5;
            const v = phi / Math.PI + 0.5;
            let w_ = 2 * Math.PI * r;
            let h_ = w_ / 2;
            positions.push(u * w_ - h_, 0, v * h_ - (h_ / 2));
        }
        render.earthObject.planPositions = positions;
        render.earthObject.earthPositions = earthPositions;
    }
    if(model=="plan"){
        render.control.enablePan = true;
        render.control.minDistance = 8000;
        if(render.sphereLineObject)render.sphereLineObject.visible = false;
        if(render.earthInnerObject)render.earthInnerObject.visible = false;
        if(render.earthDxqObject)render.earthDxqObject.visible = false;
        render.control.autoRotate = false;
        render.lightGroups.visible = false;
        render.earthObject.material = render.planMapMaterial;
        // $(".cir2,.cir1").hide();
        render.control.mouseButtons = { LEFT: THREE.MOUSE.PAN, MIDDLE: THREE.MOUSE.DOLLY, RIGHT: THREE.MOUSE.ROTATE };
        render.control.enableRotate = false;
        // render.labelRenderer.enableIntersect = false;
        if(render.planMesh) render.planMesh.visible = false;
        render.heiLight.intensity = 1;
        render.scene.children[1].intensity = 1;
        $("body").attr("code","plan");
        render.control.maxPolarAngle = THREE.MathUtils.degToRad(89);
        render.control.minPolarAngle = THREE.MathUtils.degToRad(0);
        render.control.minAzimuthAngle = Infinity;
        render.control.maxAzimuthAngle = -Infinity;
        render.setView({target:{x:-1505.9191539023175,y:-4.1175299284803975e-14,z:2124.0203267875154},position:{x:-1505.919263556004,y:20278.34552539471,z:2124.0000455859363}});
    }else{
        render.setView({target:{x:0,y:800,z:0},position:{x:17258.775269189373,y:3328.6396507272502,z:-8926.529598513758}});
        render.control.enablePan = false;
        render.control.minDistance = 15000;
        render.earthObject.visible = true;
        render.countryMeshs.visible = false;
        render.control.mouseButtons = { LEFT: THREE.MOUSE.ROTATE, MIDDLE: THREE.MOUSE.DOLLY, RIGHT: THREE.MOUSE.PAN };
        render.control.enableRotate = true;
        // render.labelRenderer.enableIntersect = true;
        render.heiLight.intensity = 0;
        render.scene.children[1].intensity = 0;
        render.control.maxPolarAngle = THREE.MathUtils.degToRad(95);
        render.control.minPolarAngle = THREE.MathUtils.degToRad(70);
        // render.control.minAzimuthAngle = THREE.MathUtils.degToRad(85);
        render.control.minAzimuthAngle = THREE.MathUtils.degToRad(0);
        render.control.maxAzimuthAngle = THREE.MathUtils.degToRad(-95);
    }
    render.animationsTool.setPosition(render.earthObject, {
        start: { x: 0 }, position: { x: 1 }, delta: 1000, update: function (_obj, param) {
            if (param.object){
                const earthPositions = param.object.earthPositions;
                const positions = [];
                for (let i = 0; i < earthPositions.length; i+=3) {
                    const x = earthPositions[i];
                    const y = earthPositions[i+1];
                    const z = earthPositions[i+2];
                    const tx = param.object.planPositions[i];
                    const ty = param.object.planPositions[i+1];
                    const tz = param.object.planPositions[i+2];
                    let np = new THREE.Vector3(x,y,z);
                    if(model=="plan"){
                        np = np.lerp(new THREE.Vector3(tx,ty,tz),_obj.x);
                    }else{
                        np = new THREE.Vector3(tx,ty,tz).lerp(np,_obj.x);
                    }
                    positions.push(np.x,np.y,np.z);
                }
                param.object.geometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
            }
        },callback:function(){
            render.earthObject.geometry.computeVertexNormals();
            render.earthObject.geometry.computeBoundingSphere();
            if(model=="earth"){
                if(render.sphereLineObject)render.sphereLineObject.visible = true;
                if(render.earthInnerObject)render.earthInnerObject.visible = true;
                if(render.earthDxqObject)render.earthDxqObject.visible = true;
                render.control.autoRotate = true;
                render.lightGroups.visible = true;
                render.earthObject.material = render.earthMaterial;
                if(render.planMesh) render.planMesh.visible = true;
                // $(".cir2,.cir1").show();
                $("body").attr("code","earth");
                if(!isNotHttp){
                    getForeignInfoByName("earth","",function(){
                        showCountryMeshs("earth");
                    })
                }
            }else{
                // render.earthObject.visible = false;
                // if(render.earthObject) render.earthObject.visible = false
                if(!isNotHttp){
                    getForeignInfoByName("earth","",function(){
                        showCountryMeshs("plan");
                    })
                }
                // render.setView({target:{x:-1505.9191539023175,y:-4.1175299284803975e-14,z:2124.0203267875154},position:{x:-1505.919263556004,y:20278.34552539471,z:2124.0000455859363}});
            }
            if(callback) callback();
        }
    })
}
function showCountryMeshs(model){
    if(model=="plan"){
        render.countryMeshs.visible = true;
        // render.labelRenderer.enableIntersect = false;
    }else{
        render.labelRenderer.intersectObjects = [render.planMesh];
        // render.labelRenderer.enableIntersect = true;
    }
    let children = render.countryMeshs.children;
    for(let i=0;i<children.length;i++){
        if(model=="plan") children[i].visible = true;
        loadCountryName(children[i],children[i].name,children[i].name_en,model);
    }
    drawEarthTextureByDatas(children);
}
function initWorldJSON(){
    $.ajax({ 
        type : "GET", //提交方式 
        url : "./data/world.json",
        async: false,
        success : function(response) {//返回数据根据结果进行相应的处理 
            initWorldSFJson();
            initWorld1Json();
           if(typeof response == "string") response = JSON.parse(response);
            let features = response.features;
            var canvas = document.createElement('canvas');
            var canvas1 = document.createElement('canvas');
            // $("body").append(canvas1);
            let w = 6371 * 2 * Math.PI / 10;
            let h = w / 2;
            canvas.width = w;
            canvas.height = h;
            canvas1.width = w;
            canvas1.height = h;
            var context = canvas.getContext('2d');
            var ctx = canvas1.getContext('2d');
            var centerX = w / 2;
            var centerY = h / 2;
            var average = w / 360;
            context.strokeStyle = "#bbb";
            ctx.strokeStyle = "#bbb";
            context.clearRect(0, 0, w, h);
            ctx.clearRect(0, 0, w, h);
            render.countryMeshs = new THREE.Group();
            render.countryMeshs.position.y = -0.1;
            render.scene.add(render.countryMeshs);
            render.countryMeshs.visible = false;
            render.countryMeshs.scale.y = render.mapHeight;
            for(let i=0;i<features.length;i++){
                let geometry = features[i].geometry;
                let coordinates = geometry.coordinates;
                let properties  = features[i].properties;
                let group = new THREE.Group();
                properties.name_en = properties.name;
                properties.name = properties.name_zh || properties.name;
                group.name = properties.name;
                group.name_en = properties.name_en;
                render.countryMeshs.add(group);
                let polys = {properties:properties,coordinates:[]};
                let maxX = -Infinity,maxY = -Infinity,minX = Infinity,minY = Infinity;
                if(geometry.type=="MultiPolygon"){
                    for(let j=0;j<coordinates.length;j++){
                        draw(context,coordinates[j][0],centerX,centerY,average);
                        draw(ctx,coordinates[j][0],centerX,centerY,average,true);
                        if(coordinates[j][0]){
                            let box = getPolyBox(coordinates[j][0]);
                            maxX = Math.max(maxX,box.maxX);
                            maxY = Math.max(maxY,box.maxY);
                            minX = Math.min(minX,box.minX);
                            minY = Math.min(minY,box.minY);
                            let mesh = getPoly(coordinates[j][0]);
                            mesh.name = properties.name;
                            mesh.userData = properties;
                            group.add(mesh);
                            polys.coordinates.push(coordinates[j][0]);
                        }
                    }
                }else{
                    draw(context,coordinates[0],centerX,centerY,average);
                    draw(ctx,coordinates[0],centerX,centerY,average,true);
                    if(coordinates[0]){
                        let box = getPolyBox(coordinates[0]);
                        maxX = Math.max(maxX,box.maxX);
                        maxY = Math.max(maxY,box.maxY);
                        minX = Math.min(minX,box.minX);
                        minY = Math.min(minY,box.minY);
                        let mesh = getPoly(coordinates[0]);
                        mesh.name = properties.name;
                        mesh.userData = properties;
                        group.add(mesh);
                        polys.coordinates.push(coordinates[0]);
                    }
                }
                polys.properties.center = [minX+(maxX - minX)/2,minY+(maxY - minY)/2];
                if(properties.name_zh=="南非"){
                    polys.properties.center = [24.286748003007858,-30.908021688876946];
                }else if(properties.name_zh=="美国"){
                    polys.properties.center = [-101.40670637596831,39.861351413549876];
                }else if(properties.name_zh=="加拿大"){
                    polys.properties.center = [-106.37702677912249,56.12935755117835];
                }
                group.polys = polys;
                // let mesh = creatEarthCountyName(group.name,"（10）",group.polys.properties.center);
                // render.scene.add(mesh);
            }
            render.earthCanvas = canvas;
            render.earthCanvas.config = {centerX,centerY,average};
            let texture = new THREE.CanvasTexture(canvas);
            render.earthMaterial.map = texture;
            render.earthMaterial.needsUpdate = true;
            var canvas2 = document.createElement('canvas');
            canvas2.width = canvas.width;
            canvas2.height = canvas.height;
            let ctx1 = canvas2.getContext("2d");
            var gradient = ctx1.createLinearGradient(0, 0, canvas2.width, 0); // 从左到右
            gradient.addColorStop(0, 'rgba(18, 34, 77,0)');
            gradient.addColorStop(0.5, 'rgba(18, 34, 77,0.3)');  
            gradient.addColorStop(1, 'rgba(18, 34, 77,0)'); 
            // ctx1.fillStyle = "rgba(18, 34, 77,0.3)";
            ctx1.fillStyle = gradient;
            ctx1.fillRect(0,50,w,h - 100);
            ctx1.drawImage(canvas1,0,0);
            let texture1 = new THREE.CanvasTexture(canvas2);
            render.planMapMaterial.map = texture1;
            render.planMapMaterial.needsUpdate = true;
        }
   })
}
function drawEarthTextureByDatas(objects){
    var canvas = document.createElement('canvas');
    canvas.width = render.earthCanvas.width;
    canvas.height = render.earthCanvas.height;
    let ctx = canvas.getContext("2d");
    ctx.drawImage(render.earthCanvas,0,0);
    for(let i=0;i<objects.length;i++){
        let name = objects[i].name;
        if(render.foreignInfoEarth&&render.foreignInfoEarth[name]){
            // let names = render.foreignInfoEarth[name].split(":");
            // render.foreignInfoEarth[name] = names[0];
            // let code = "";
            // if(names[1]>0) code = "yd";
            // if(model=="plan"){
            //     center = lonlatToPlanVector3(object.polys.properties.center[0],object.polys.properties.center[1],render.earthSize);
            //     render.setInfoWindow(`<div class="countryName" type="country" code="${code}" onClick="inCountry('${name}')" adcode="${name}">${name}（${render.foreignInfoEarth[name]}）</div>`,{x:center.x,y:0,z:center.z},{name:name});
            // }else{
            //     center = lonlatToEarthVector3(object.polys.properties.center[0],object.polys.properties.center[1],render.earthSize);
            //     render.setInfoWindow(`<div class="countryName" type="country" code="${code}" onClick="inCountryByEarth('${name}')" adcode="${name}">${name}（${render.foreignInfoEarth[name]}）</div>`,{x:center.x,y:center.y,z:center.z},{name:name});
            // }
            // if(render.infoWindos[name]) render.infoWindos[name].isAvoid = true;
            let coordinates = objects[i].polys.coordinates;
            for(let k=0;k<coordinates.length;k++){
                drawEarthCanvas(ctx,coordinates[k],render.earthCanvas.config.centerX,render.earthCanvas.config.centerY,render.earthCanvas.config.average,name);
            }
        }
    }
    let texture = new THREE.CanvasTexture(canvas);
    render.earthMaterial.map = texture;
    render.earthMaterial.needsUpdate = true;
}
function drawEarthCanvas(ctx,datas,centerX,centerY,average,name){
    // ctx.closePath();
    if(!datas) return;
    ctx.beginPath();
    // ctx.fillStyle = 'rgba(26, 73, 112,1)';
    ctx.fillStyle = 'rgb(46, 106, 197)';
    // 设置阴影属性
    ctx.shadowOffsetX = 0;       // 水平方向阴影偏移量
    ctx.shadowOffsetY = 0;       // 垂直方向阴影偏移量
    ctx.shadowBlur = 3;        // 阴影模糊程度
    ctx.shadowColor = 'rgba(106, 186, 255,1)'; // 阴影颜色
    ctx.shadowColor = '#fff'; // 阴影颜色
    ctx.strokeStyle = "#ffffff";
    for(let i=0;i<datas.length;i++){
        if(i==0){
            ctx.moveTo(centerX + datas[i][0] * average, centerY - datas[i][1] * average);
            // ctx.fillText(name,centerX + datas[i][0] * average, centerY - datas[i][1] * average)
        }else{
            ctx.lineTo(centerX + datas[i][0] * average, centerY - datas[i][1] * average);
        }
    }
    ctx.fill();//stroke
    ctx.stroke();
    ctx.closePath();
}
function loadCountryName(object,name,name_en,model){
    if(!object) return;
    let center = {x:0,y:0,z:0};
    // console.log(datas.data[0][name],name,datas.data[0],"--------");
    if(!render.earthCountyNames){
        render.earthCountyNames = new THREE.Group();
        render.scene.add(render.earthCountyNames);
    }
    if(render.foreignInfoEarth&&render.foreignInfoEarth[name]){
        let names = render.foreignInfoEarth[name].split(":");
        // render.foreignInfoEarth[name] = names[0];
        let num = names[0];
        let code = "";
        if(names[1]>0) code = "yd";
        let showNum = "";
        if(num>0) showNum = `（${num}）`;
        if(model=="plan"){
            let showName = render.language=="cn"?name:name_en;
            if(showName=="刚果共和国（刚果（布）") showName = "刚果（布）";
            else if(showName=="刚果共和国（刚果（金）") showName = "刚果（金）";
            else if(showName=="阿拉伯联合酋长国（阿联酋）") showName = "阿联酋";
            center = lonlatToPlanVector3(object.polys.properties.center[0],object.polys.properties.center[1],render.earthSize);
            // render.setInfoWindow(`<div class="countryName" type="country" code="${code}" onClick="inCountry('${name}')" onTouchstart="touchePointEvent()" onTouchend="inCountry('${name}')" adcode="${name}">${showName}</br>${showNum}</div>`,{x:center.x,y:0,z:center.z},{name:name});
            
            let mesh = creatEarthCountyName1(`${render.language=="cn"?name:name_en}`,showNum,center);
            // if(code=="yd") mesh.material.color = new THREE.Color("#fece61")
            if(num<=0 && names[1]<=0) mesh.material.color = new THREE.Color("#fff")
            render.earthCountyNames.add(mesh);
            mesh.name = name;
            mesh.name_en = name_en;
            if(num>0 || names[1]>0){
                render.events.on("click",mesh,function(obj){
                    inCountryByEarth(obj.name,obj.name_en);
                })
                render.events.on("touchstart",mesh,function(obj,p,p1){
                    render.touchePoint = p1;
                    inCountryByEarth(obj.name,obj.name_en);
                })
            }
        }else{
            // center = lonlatToEarthVector3(object.polys.properties.center[0],object.polys.properties.center[1],render.earthSize);
            // render.setInfoWindow(`<div class="countryName" type="country" code="${code}" onClick="inCountryByEarth('${name}')" onTouchstart="touchePointEvent()" onTouchend="inCountryByEarth('${name}')" adcode="${name}">${name}（${render.foreignInfoEarth[name]}）</div>`,{x:center.x,y:center.y,z:center.z},{name:name});
            let mesh = creatEarthCountyName(`${render.language=="cn"?name:name_en}`,showNum,object.polys.properties.center);
            // if(code=="yd") mesh.material.color = new THREE.Color("#fece61")
            if(num<=0 && names[1]<=0) mesh.material.color = new THREE.Color("#fff")
            render.earthCountyNames.add(mesh);
            mesh.name = name;
            mesh.name_en = name_en;
            if(num>0 || names[1]>0){
                render.events.on("click",mesh,function(obj){
                    inCountryByEarth(obj.name,obj.name_en);
                })
                render.events.on("touchstart",mesh,function(obj,p,p1){
                    render.touchePoint = p1;
                    inCountryByEarth(obj.name,obj.name_en);
                })
            }
        }
        if(render.infoWindos[name]) render.infoWindos[name].isAvoid = true;
    }
}
function draw(ctx,datas,centerX,centerY,average,isNotFill){
    // ctx.closePath();
    if(!datas) return;
    let maxX = -Infinity,maxY = -Infinity,minX = Infinity,minY = Infinity;
    for(let i=0;i<datas.length;i++){
        maxX = Math.max(centerX + datas[i][0] * average,maxX);
        maxY = Math.max(centerY - datas[i][1] * average,maxY);
        minX = Math.min(centerX + datas[i][0] * average,minX);
        minY = Math.min(centerY - datas[i][1] * average,minY);
    }
    let w = Math.abs((maxX - minX));
    let h = Math.abs((maxY - minY));
    let r = Math.max(w,h);
    let cx = minX + w / 2;
    let cy = minY + h / 2;
    // if(r<10) return;
    // console.log(w,h,"-------")
    ctx.beginPath();
    // 创建径向渐变，从中心向外扩展
    // var gradient = ctx.createRadialGradient(cx, cy, 0, cx, cy, Math.min(w,h));
    // gradient.addColorStop(0, 'rgba(106, 186, 255,0)'); // 开始颜色为白色
    // // gradient.addColorStop(0.1, 'rgba(106, 186, 255,0)'); // 开始颜色为白色
    // // gradient.addColorStop(0.2, 'rgba(106, 186, 255,0)'); // 开始颜色为白色
    // gradient.addColorStop(1, '#6abaff');  // 结束颜色为蓝色
    // ctx.fillStyle = 'rgba(26, 73, 112,1)';
    ctx.fillStyle = 'rgb(13, 83, 167)';
    // 设置阴影属性
    ctx.shadowOffsetX = 2;       // 水平方向阴影偏移量
    ctx.shadowOffsetY = 2;       // 垂直方向阴影偏移量
    ctx.shadowBlur = 3;        // 阴影模糊程度
    // ctx.shadowColor = 'rgba(106, 186, 255,1)'; // 阴影颜色
    ctx.shadowColor = '#333'; // 阴影颜色
    ctx.lineWidth = 2;
    for(let i=0;i<datas.length;i++){
        if(i==0){
            ctx.moveTo(centerX + datas[i][0] * average, centerY - datas[i][1] * average);
        }else{
            ctx.lineTo(centerX + datas[i][0] * average, centerY - datas[i][1] * average);
        }
    }
    if(!isNotFill)ctx.fill();//stroke
    ctx.stroke();
    ctx.closePath();
}
function getSprite(img){
    let material = null;
    if(!render.spriteMaterials) render.spriteMaterials = {};
    if(render.spriteMaterials[img]){
        material = render.spriteMaterials[img];
    }else{
        const map = new THREE.TextureLoader().load( img );
        material = new THREE.SpriteMaterial( { map: map,transparent:true,sizeAttenuation:true,blending:2 } );
        render.spriteMaterials[img] = material;
    }
    const sprite = new THREE.Sprite( material );
    sprite.renderOrder = 99;
    return sprite;
}
function getRandomInt(min, max) {
    return Math.floor(Math.random() * (max - min)) + min;
}
function initStars(geometry){
    let positions = geometry.getAttribute("position").array;
    let sprite = getSprite("textures/star.png");
    let sprite_ = getSprite("textures/circle.png");
    let newPositions = [];
    for(let i=0;i<positions.length;i+=3){
        let x = positions[i] + getRandomInt(-200,200);
        let y = positions[i+1];
        let z = positions[i+2] + getRandomInt(-200,200);
        newPositions.push(x,y,z);
        let sprite1 = sprite.clone();
        let scale = getRandomInt(300,600);
        sprite1.position.set(x,y,z);
        sprite1.scale.set(scale,scale,scale);
        let sprite2 = sprite_.clone();
        sprite2.position.set(x,y,z);
        sprite2.scale.set(200,200,200);
        render.sphereLineObject.add(sprite1);
        render.sphereLineObject.add(sprite2);
    }
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(newPositions, 3));
    geometry.computeVertexNormals();
    geometry.computeBoundingSphere();
}
function createEarthBg(){
    var canvas = document.createElement('canvas');
    // $("body").append(canvas);
    canvas.width = 256;
    canvas.height = canvas.width / 2;
    var ctx = canvas.getContext('2d');
    var gradient = ctx.createRadialGradient(canvas.width / 2, canvas.height / 2, 0, canvas.width / 2, canvas.height / 2, canvas.width / 4);
    // gradient.addColorStop(0, 'rgba(0, 0, 0, 0)'); 
    // gradient.addColorStop(0.4, 'rgba(11, 55, 107,1)'); 
    // gradient.addColorStop(1, 'rgba(13, 78, 158,1)'); 
    gradient.addColorStop(0, 'rgb(13, 83, 167)'); 
    gradient.addColorStop(0.4, 'rgb(13, 83, 167)'); 
    gradient.addColorStop(1, 'rgb(24, 125, 251)'); 
    ctx.fillStyle = gradient;
    ctx.fillRect(0,0,canvas.width,canvas.height);
    let texture = new THREE.CanvasTexture(canvas);
    return texture;
}
function createEarthBloomBg(){
    var canvas = document.createElement('canvas');
    // $("body").append(canvas);
    canvas.width = 256;
    canvas.height = canvas.width / 2;
    var ctx = canvas.getContext('2d');
    var gradient = ctx.createRadialGradient(canvas.width / 2, canvas.height / 2, 0, canvas.width / 2, canvas.height / 2, canvas.width / 4);
    gradient.addColorStop(0, 'rgba(0, 0, 0, 0)'); 
    // gradient.addColorStop(0.4, 'rgba(11, 55, 107,1)'); 
    // gradient.addColorStop(1, 'rgba(13, 78, 158,1)'); 
    gradient.addColorStop(0.7, 'rgb(13, 83, 167)');  
    gradient.addColorStop(1, 'rgba(24, 125, 251,1)'); 
    ctx.fillStyle = gradient;
    ctx.fillRect(0,0,canvas.width,canvas.height);
    let texture = new THREE.CanvasTexture(canvas);
    return texture;
}
//绘制城市边界
function createCityBound(w,h,sw,sh,poins,title){
    var canvas = document.createElement('canvas');
    canvas.width = 512;
    canvas.height = 512;
    var ctx = canvas.getContext('2d');
    ctx.clearRect(0,0,w,h);
    ctx.lineWidth = 5;
    ctx.strokeStyle = "#fff";
    let wR = w / 512,hR = h/512;
    for(let i=0;i<poins.length;i+=2){
        if(i==0){
            ctx.moveTo((poins[i].x - sw)/wR,(poins[i].z - sh)/hR);
        }else{
            ctx.lineTo((poins[i].x - sw)/wR,(poins[i].z - sh)/hR);
        }
    }
    ctx.stroke();
    if(title){
        ctx.fillText(title,w/2,h/2);
    }
    // let texture = new THREE.CanvasTexture(canvas);
    return canvas;
}
function getPolyBox(points){
    let maxX = -Infinity,minX = Infinity,maxY = -Infinity,minY = Infinity;
    for(let i=0;i<points.length;i++){
        maxX = Math.max(maxX,points[i][0]);
        maxY = Math.max(maxY,points[i][1]);
        minX = Math.min(minX,points[i][0]);
        minY = Math.min(minY,points[i][1]);
    }
    return {minX,minY,maxX,maxY}
}
//绘制面
function getPoly(points){
    let shape = new THREE.Shape();
    let maxX = -Infinity,minX = Infinity,maxY = -Infinity,minY = Infinity;
    let cityPoints = [];
    // console.log(points)
    for(let i=0;i<points.length;i++){
        let point = lonlatToPlanVector3(points[i][0],points[i][1],render.earthSize);
        maxX = Math.max(maxX,point.x);
        maxY = Math.max(maxY,point.z);
        minX = Math.min(minX,point.x);
        minY = Math.min(minY,point.z);
        if(i==0){
            shape.moveTo(point.x,point.z);
        }else{
            shape.lineTo(point.x,point.z);
        }
        cityPoints.push(point);
    }
    let xLen = maxX - minX;
    let yLen = maxY - minY;
    let depth = 1;
    // if(Math.min(xLen,yLen)<50) depth = 0.01;
    let geometry = new THREE.ExtrudeGeometry( shape,{ depth: depth, bevelEnabled: false, bevelSegments: 2, steps: 2, bevelSize: 1, bevelThickness: 1 } );
    let positions = geometry.attributes.position.array;
    let newUvs = [];
    for(let i=0;i<positions.length;i+=3){
        let u = (positions[i] - minX) / xLen;
        let v = 1 - (positions[i+1] - minY) / yLen;
        newUvs.push(u,v);
    }
    // let canvas = createCityBound(xLen,yLen,minX,minY,cityPoints,"test");

    geometry.setAttribute('uv', new THREE.Float32BufferAttribute(newUvs, 2));
    if(!render.mapMaterial) {
        let texture = new THREE.TextureLoader().load("textures/planMap.png");
        // let texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = texture.wrapT = THREE.RepeatWrapping;
        render.mapMaterial = new THREE.MeshStandardMaterial({color:0xffffff,side:2,map:texture,transparent:true,metalness:0.3,roughness:0});
        // render.mapMaterial1 = new THREE.MeshStandardMaterial({color:0x888888,side:2});
    }
    let mesh = new THREE.Mesh( geometry, render.mapMaterial);
    // if(depth == 0.01){
    //     mesh.material = render.mapMaterial1;
    // }
    mesh.rotation.x = THREE.MathUtils.degToRad(90);
    // group.add( mesh );
    return mesh;
}
function getJsonFeatures(features){
    let _features = [];
    for(let i=0;i<features.length;i++){
        let geometry = features[i].geometry;
        let coordinates = geometry.coordinates;
        let _coordinates = [];
        if(geometry.type=="MultiPolygon"){
            for(let j=0;j<coordinates.length;j++){
                _coordinates.push(coordinates[j][0]);
            }
        }else{  
            _coordinates.push(coordinates[0]);
        }
        _features.push({properties:features[i].properties,coordinates:_coordinates});
    }
    return _features;
}
function inCountryByEarth(country,name_en){
    // if(event&&event.isTrusted&&event.changedTouches){
    //     if(Math.abs(event.changedTouches[0].clientX - render.touchePoint.x)>2 || Math.abs(event.changedTouches[0].clientY - render.touchePoint.y)>2) return;
    // }
    // changeModel("plan",function(){
    //     // console.log(country)
    // },true)
    render.earthObject.currModel = "plan";
    clearModels();
    render.control.enablePan = true;
    render.control.minDistance = 8000;
    if(render.sphereLineObject)render.sphereLineObject.visible = false;
    if(render.earthInnerObject)render.earthInnerObject.visible = false;
    if(render.earthDxqObject)render.earthDxqObject.visible = false;
    render.control.autoRotate = false;
    render.lightGroups.visible = false;
    render.earthObject.material = render.planMapMaterial;
    // $(".cir2,.cir1").hide();
    render.control.mouseButtons = { LEFT: THREE.MOUSE.PAN, MIDDLE: THREE.MOUSE.DOLLY, RIGHT: THREE.MOUSE.ROTATE };
    render.control.enableRotate = false;
    // render.labelRenderer.enableIntersect = false;
    if(render.planMesh) render.planMesh.visible = false;
    render.heiLight.intensity = 1;
    render.scene.children[1].intensity = 1;
    $("body").attr("code","plan");
    render.control.maxPolarAngle = THREE.MathUtils.degToRad(89);
    render.control.minPolarAngle = THREE.MathUtils.degToRad(0);
    render.control.minAzimuthAngle = Infinity;
    render.control.maxAzimuthAngle = -Infinity;
    inCountry(country,"earth",name_en);
}
//进入国家
function inCountry(country,type,name_en){
    if(event&&event.isTrusted&&event.changedTouches){
        if(Math.abs(event.changedTouches[0].clientX - render.touchePoint.x)>2 || Math.abs(event.changedTouches[0].clientY - render.touchePoint.y)>2) return;
    }
    if(!render.countrys) render.countrys = [];
    render.countrys.push(country);
    render.control.minDistance = 4000;
    render.earthObject.visible = false;
    render.countryMeshs.visible = false;
    render.control.enableRotate = false;
    clearModels();
    $(".jdx").css({display:"none",filter:"drop-shadow(0px 0px 0px #2e72d7)"});
    if(type=="earth") render.inCountryType = "earth";
    else render.inCountryType = "plan";
    window.parent.postMessage({eve:"clickTitle",title:country,level:"country"},"*");
    render.currCountry = country;
    if(country=="China" || country=="中国"){
        initGeoJsonDataByCode(100000,function(result){
            getForeignInfoByName("country",country,function(datas){
                // drawCountry(result,{},true);
                drawCountry(result,datas,true);
                render.control.minDistance = 3000;
                render.viewAnimate({target:{x:-11565.974659092411,y:1.5049999999999955,z:3933.2666557392763},position:{x:-11565.974729472986,y:5017.563704732928,z:3933.2616399513704},time:500});
                // $(".jdx").show();
                $(".jdx").css({display:"block",filter:"drop-shadow(0px 0px 6px #2e72d7)"});
            })
        })
        render.currCountrysModel.level = "province";
    }else{
        let country_ = render.countryMeshs.getObjectByName(country);
        render.currCountrysModel.level = "province";
        if(country_){
            // console.log(render.worldSFDatas[name_en],name_en,"---------");
            let outF = render.world1Datas[name_en] ? [render.world1Datas[name_en]] : [country_.polys];
            if(country=="美国" || country=="加拿大"){
                if(country=="美国"){
                    outF = [{coordinates:[country_.polys.coordinates[25]],properties:country_.polys.properties}];
                }else{
                    outF = [country_.polys];
                }
            }
            drawCountry({outF:outF,inF:render.worldSFDatas[name_en] || []},"","",country);
            initDemoInfoByName(country,true);
            // if(country=="美国"){
            //     render.viewAnimate({target:{x:12158.639723169837,y:0.005000000301831134,z:4133.959593025788},position:{x:12247.14067011556,y:6583.570409193801,z:11.892295720014772},time:500});
            // }
        }
    }
}
function clearCountyNames(){
    if(render.earthCountyNames){
        let children = render.earthCountyNames.children;
        let removeObjects = [];
        for(let i=0;i<children.length;i++){
            // console.log(children.length,"--------")
            if(children[i].geometry) children[i].geometry.dispose();
            if(children[i].material) children[i].material.dispose();
            removeObjects.push(children[i]);
        }
        for(let i=0;i<removeObjects.length;i++){
            render.events.off("click",removeObjects[i])
            render.events.off("touchstart",removeObjects[i]);
            render.earthCountyNames.remove(removeObjects[i]);   
        }
        if(render.labelRenderer.showAvoids)render.labelRenderer.showAvoids.clear()
    }
}
function clearDemoInfosModel(){
    if(render.currDemoInfosModel){
        let children = render.currDemoInfosModel.children;
        let length = children.length;
        let removeObjects = [];
        for(let i=0;i<length;i++){
            if(children[i].geometry) children[i].geometry.dispose();
            if(children[i].material){
                children[i].material.dispose();
                if(children[i].material.map) children[i].material.map.dispose();
            }
            removeObjects.push(children[i]);
        }
        for(let i=0;i<removeObjects.length;i++){
            render.currDemoInfosModel.remove(removeObjects[i]);   
        }
    }
}
function clearModels(){
    if(render.currCountrysModel){
        let children = render.currCountrysModel.children;
        let length = children.length;
        let removeObjects = [];
        for(let i=0;i<length;i++){
            // console.log(children.length,"--------")
            if(children[i].geometry) children[i].geometry.dispose();
            if(children[i].material){
                children[i].material.dispose();
                if(children[i].material.map) children[i].material.map.dispose();
            }
            removeObjects.push(children[i]);
        }
        for(let i=0;i<removeObjects.length;i++){
            render.currCountrysModel.remove(removeObjects[i]);   
        }
    }
    if(render.xmInfoTimer) clearInterval(render.xmInfoTimer);
    render.clearInfoWindows();
    clearCountyNames();
    clearDemoInfosModel();
    // $(".jdx").hide();
    $(".jdx").css({display:"none",filter:"drop-shadow(0px 0px 0px #2e72d7)"});
    if(render.labelRenderer.showAvoids)render.labelRenderer.showAvoids.clear()
}
function drawCountry(datas,demoDatas,isNotView,country){
    if(!demoDatas) demoDatas = {};
    clearModels();
    // console.log(datas,"========")
    let outF = datas.outF;
    let maxLen = -Infinity,currPolys = [];
    for(let i=0;i<outF.length;i++){
        let coordinates = outF[i].coordinates;
        for(let j=0;j<coordinates.length;j++){
            let mesh = getPoly(coordinates[j]);
            let box = getObjectBox(mesh);
            let length = Math.max(maxLen,Math.max(box.max.x-box.min.x,box.max.z-box.min.z));
            if(length>maxLen){
                maxLen = length;
                currPolys = coordinates[j];
                render.currCountrysModel.currPolys = currPolys;
            }
            render.currCountrysModel.add(mesh);
        }
    }
    let box = getObjectBox(render.currCountrysModel);
    // let length = Math.sqrt((box.max.x - box.min.x) * (box.max.x - box.min.x) + (box.max.z - box.min.z) * (box.max.z - box.min.z));
    let length = Math.max((box.max.x - box.min.x) / 3,(box.max.z - box.min.z));
    getCountryTexture(datas,demoDatas,length,country);
    let center = box.getCenter(new THREE.Vector3());
    render.control.minDistance = length / 10;
    render.currCountrysModel.scale.y = length / 40;
    render.currCountrysModel.center = center;
    // if(render.currCountrysModel.scale.y < 200) render.currCountrysModel.scale.y = 200;
    if(isNotView) return;
    // render.viewAnimate({target:{x:center.x,y:box.max.y+0.005,z:center.z},position:{x:center.x,y:length,z:center.z - length / 1.3},time:500})
    render.viewAnimate({target:{x:center.x,y:box.max.y+0.005,z:center.z},position:{x:center.x,y:length * 1.5,z:center.z - 0.1},time:500})
    // if(datas.inF&&datas.inF.length > 0){
    //     render.viewAnimate({target:{x:center.x,y:box.max.y+0.005,z:center.z},position:{x:center.x,y:length,z:center.z - length / 1.3},time:500})
    // }else{
    //     render.viewAnimate({target:{x:center.x,y:box.max.y+0.005,z:center.z},position:{x:center.x,y:length,z:center.z + length / 1.3},time:500})
    // }
}
function getObjectBox(object) {
    var box = new THREE.Box3();
    box.setFromObject(object);
    return box;
}
//获取城市贴图
function getCountryTexture(datas,demoDatas,length,country){
    // console.log(datas,"=============")
    if(!demoDatas) demoDatas = {};
    let outF = datas.outF;
    let inF = datas.inF;
    let maxX = -Infinity,minX = Infinity,maxY = -Infinity,minY = Infinity;
    let positions = [];
    let showName = "";
    let tW = 2048;
    render.currCountryNames = {};
    for(let i=0;i<outF.length;i++){
        let coordinates = outF[i].coordinates;
        let properties = outF[i].properties;
        if(render.currCountrysModel.level == "province"){
            render.currCityName = properties.name;
        }
        render.currCountrysModel.acroutes = properties.acroutes;
        for(let j=0;j<coordinates.length;j++){
            positions[j] = [];
            for(let p=0;p<coordinates[j].length;p++){
                let point = lonlatToPlanVector3(coordinates[j][p][0],coordinates[j][p][1],render.earthSize);
                maxX = Math.max(maxX,point.x);
                maxY = Math.max(maxY,point.z);
                minX = Math.min(minX,point.x);
                minY = Math.min(minY,point.z);
                positions[j].push(point);
            }
        }
        if(inF.length==0&&properties || country&&properties){
            let centroid = properties.centroid || properties.center;
            if(centroid || properties.countryCenter){
                let point = {x:0,y:0,z:0};
                if(properties.countryCenter) point = properties.countryCenter;
                else point = lonlatToPlanVector3(centroid[0],centroid[1],render.earthSize);
                showName = properties.name;
                // render.setInfoWindow(`<div style="pointer-events:none;" class="countryName" name_en="${properties.name_en}" name_cn="${properties.name}" type="${properties.level}" adcode="${properties.adcode}"><span>${render.language=="cn"?properties.name:properties.name_en}</span></div>`,{x:point.x,y:0,z:point.z},{name:properties.name});
                // if(render.infoWindos[properties.name]) render.infoWindos[properties.name].isAvoid = true;
                let mesh = creatEarthCountyName1(country || properties.name,"",point);
                mesh.material.color = new THREE.Color("#aaa")
                let w = Math.abs((maxX - minX));
                let h = Math.abs((maxY - minY));
                let wR = (w / tW) || 1;
                let hR = (h / tW) || 1;
                const r = Math.min(wR,hR);
                mesh.scale.x = mesh.scale.x * r * 0.8;
                mesh.scale.y = mesh.scale.y * r * 0.8;
                mesh.material.depthTest = false;
                // render.earthCountyNames.add(mesh);
                render.currCountryNames["center"] = point;
            }
        }
    }
    // console.log(maxX,maxY,minX,minY,positions,"----------");
    let w = Math.abs((maxX - minX));
    let h = Math.abs((maxY - minY));
    let wR = (w / tW) || 1;
    let hR = (h / tW) || 1;
    render.currBoxConfig = {w,h,minX,minY,maxX,maxY};
    var canvas = document.createElement('canvas');
    canvas.width = canvas.height = tW;
    // $("body").append(canvas);
    let ctx = canvas.getContext("2d");
    ctx.clearRect(0,0,tW,tW);
    // var gradient = ctx.createLinearGradient(0, 0, canvas.width, 0); // 从左到右
    var gradient = ctx.createRadialGradient(canvas.width / 2, canvas.height / 2, 0, canvas.width / 2, canvas.height / 2, tW);
    gradient.addColorStop(0, 'rgba(136, 216, 255,1)');
    gradient.addColorStop(0.2, 'rgba(136, 216, 255,0.4)'); 
    gradient.addColorStop(0.5, 'rgba(136, 216, 255,0.1)');  
    gradient.addColorStop(0.9, 'rgba(136, 216, 255,0.4)'); 
    gradient.addColorStop(1, 'rgba(136, 216, 255,1)'); 
    ctx.strokeStyle = gradient;
    ctx.lineWidth = 2;
    ctx.shadowBlur = 3;        // 阴影模糊程度
    ctx.shadowColor = 'rgba(136, 216, 255,1)'; // 阴影颜色
    let planGeometry = new THREE.PlaneBufferGeometry(1,1);
    for(let i=0;i<inF.length;i++){
        let coordinates = inF[i].coordinates;
        let properties = inF[i].properties;
        for(let j=0;j<coordinates.length;j++){
            ctx.beginPath();
            for(let p=0;p<coordinates[j].length;p++){
                let point = lonlatToPlanVector3(coordinates[j][p][0],coordinates[j][p][1],render.earthSize);
                if(p==0) ctx.moveTo((point.x - minX) / wR,(point.z - minY) / hR);
                else ctx.lineTo((point.x - minX) / wR,(point.z - minY) / hR);
            }
            ctx.strokeStyle = gradient;
            //有数据填充色块
            // if(demoDatas[properties.name]){
            //     ctx.fillStyle = "rgba(57, 131, 164,0.2)";
            //     ctx.fill();
            //     ctx.strokeStyle = "rgb(128, 217, 255)";
            // }
            ctx.stroke();
            ctx.closePath();
        }
        // demoDatas
        if(properties){
            if(properties.level) render.currCountrysModel.level = properties.level;
            let centroid = properties.centroid || properties.center;
            if((centroid&&(demoDatas[properties.name])) || properties.isGW){
            // if(centroid){
                // let point = lonlatToPlanVector3(centroid[0],centroid[1],render.earthSize);
                // render.setInfoWindow(`<div class="countryName" type="${properties.level}" onClick="clickCountryName(${properties.adcode},false,'${properties.name}','${properties.level}')" onTouchstart="touchePointEvent()" onTouchend="clickCountryName(${properties.adcode},false,'${properties.name}','${properties.level}')" adcode="${properties.adcode}"><span>${properties.name}</span></br>（${demoDatas[properties.name] || 0}）</div>`,{x:point.x,y:0,z:point.z},{name:properties.name});
                // if(render.infoWindos[properties.name]){
                //     render.infoWindos[properties.name].isAvoid = true;
                //     render.infoWindos[properties.name].userData = {outF:[inF[i]],inF:[]};
                // }
                let point = lonlatToPlanVector3(centroid[0],centroid[1],render.earthSize);
                let showName = demoDatas[properties.name] > 0 ? `（${demoDatas[properties.name] || 0}）` : "";
                if(render.currCountrysModel.level == "city" && render.currCityName != "江苏省") showName = "";
                else if(render.currCountrysModel.level == "district")  showName = "";
                let isImage = false;
                if((render.currCountrysModel.level != "city" && render.currCountrysModel.level != "district") || (render.currCityName=="江苏省" && render.currCountrysModel.level == "city")){
                    isImage = false;
                }else{
                    isImage = true;
                }
                if(properties.isGW) isImage = true;
                let mesh = creatEarthCountyName1(properties.name,showName,point,"",isImage);
                if(render.currCountry=="中国" && render.currCountrysModel.level=="province"){
                    if(demoDatas[properties.name] == 0) mesh.material.color = new THREE.Color("#fff");
                }else{
                    mesh.material.color = new THREE.Color("#fff")
                }
                render.currCountryNames[properties.name] = point;
                if(properties.isGW){
                    mesh.visible = false;
                    point.mesh = mesh;
                }
                render.earthCountyNames.add(mesh);
                mesh.name = properties.name;
                mesh.adcode = properties.adcode;
                mesh.level = properties.level;
                mesh.position.y = 0.1;
                // mesh.material.polygonOffsetFactor = -20;
                const r = Math.sqrt(wR * wR + hR * hR);
                // const r = Math.max(wR,hR);
                if(render.currCountry=="中国" && render.currCountrysModel.level=="province"){
                    mesh.scale.x = mesh.scale.x * r * 0.1;
                    mesh.scale.y = mesh.scale.y * r * 0.1;
                }else{
                    mesh.scale.x = mesh.scale.x * r * 0.2;//rX * 0.04;
                    mesh.scale.y = mesh.scale.y * r * 0.2;//rY * 0.04;
                }
                mesh.material.depthTest = false;
                mesh.userData = {outF:[inF[i]],inF:[]};
                if((render.currCountrysModel.level != "city" && render.currCountrysModel.level != "district") || (render.currCityName=="江苏省" && render.currCountrysModel.level == "city")){
                    if(demoDatas[properties.name] != 0){
                        render.events.on("click",mesh,function(obj){
                            clickCountryName(obj.adcode,false,obj.name,obj.level,obj);
                        })
                        render.events.on("touchstart",mesh,function(obj,p,p1){
                            render.touchePoint = p1;
                            clickCountryName(obj.adcode,false,obj.name,obj.level,obj);
                        })
                    }
                }
                // render.events.on("click",mesh,function(obj){
                //     clickCountryName(obj.adcode,false,obj.name,obj.level,obj);
                // })
                // render.events.on("touchstart",mesh,function(obj,p,p1){
                //     render.touchePoint = p1;
                //     clickCountryName(obj.adcode,false,obj.name,obj.level,obj);
                // })
            }
        }
    }
    ctx.lineWidth = 3;
    ctx.strokeStyle = "#fff";
    ctx.shadowBlur = 10;        // 阴影模糊程度
    ctx.shadowColor = '#fff'; // 阴影颜色
    for(let i=0;i<positions.length;i++){
        let points = positions[i];
        ctx.beginPath();
        for(let j=0;j<points.length;j++){
            if(j==0) ctx.moveTo((points[j].x - minX) / wR,(points[j].z - minY) / hR);
            else ctx.lineTo((points[j].x - minX) / wR,(points[j].z - minY) / hR);
        }
        ctx.stroke();
        ctx.closePath();
    }
    // let showNameW = 0;
    // if(showName){
    //     ctx.font = "bold 150px sans-serif";
    //     const textW = ctx.measureText(showName).width;
    //     ctx.fillStyle = "#aaa";
    //     ctx.font = "bold 150px sans-serif";
    //     ctx.strokeStyle = "#000";
    //     ctx.lineWidth = 10;
    //     ctx.shadowBlur = 2;        // 阴影模糊程度
    //     ctx.shadowColor = '#000'; // 阴影颜色
    //     ctx.strokeText(showName,canvas.width / 2 - textW / 2,canvas.height/2);
    //     ctx.fillText(showName,canvas.width / 2 - textW / 2,canvas.height/2);
    //     showNameW = textW;
    // }

    let ver = [
        // minX,0,maxY,maxX,0,maxY,minX,0,minY,maxX,0,minY
        minX,0,minY,maxX,0,minY,minX,0,maxY,maxX,0,maxY
    ]
    planGeometry.setAttribute('position', new THREE.Float32BufferAttribute(ver, 3));
    planGeometry.computeVertexNormals();
    planGeometry.computeBoundingSphere();
    let material = new THREE.MeshStandardMaterial({color:0xffffff,transparent:true,depthWrite:false,map:new THREE.CanvasTexture(canvas),side:0});
    let mesh = new THREE.Mesh(planGeometry,material);
    mesh.position.y = 0.005;
    mesh.renderOrder = 999;
    // let textNamePlan = new THREE.PlaneBufferGeometry(1,1);
    // let material_ = new THREE.MeshStandardMaterial({color:0xffffff,side:2});
    // let textMesh = new THREE.Mesh(planGeometry,material_);
    // // textMesh.position.set(minX+(maxX - minX) / 2,0,minY+(maxY - minY) / 2);
    // // textMesh.scale.set(showNameW,150,1);
    // render.currCountrysModel.add(textMesh);
    render.currCountrysModel.add(mesh);
}
function clickCountryName(adcode,isOut,name,level,mesh){
    if(event&&event.isTrusted&&event.changedTouches){
        if(Math.abs(event.changedTouches[0].clientX - render.touchePoint.x)>2 || Math.abs(event.changedTouches[0].clientY - render.touchePoint.y)>2) return;
    }
    if(!isOut&&render.countrys[render.countrys.length-1] != name){
        render.countrys.push(name);
    }
    window.parent.postMessage({eve:"clickTitle",title:name,level:render.currCountrysModel.level},"*");
    if(!isOut&&level=="district"){
        // console.log("最低级别");
        if(name&&mesh){
            render.currCountrysModel.level = "county";
            drawCountry(mesh.userData);
            initDemoInfoByName(name);
        }
        return;
    }
    // if((render.currCountrysModel.level != "city" && render.currCountrysModel.level != "district") || (render.currCityName=="江苏省" && render.currCountrysModel.level == "city")){
    //     render.events.on("click",mesh,function(obj){
    //         clickCountryName(obj.adcode,false,obj.name,obj.level,obj);
    //     })
    //     render.events.on("touchstart",mesh,function(obj,p,p1){
    //         render.touchePoint = p1;
    //         clickCountryName(obj.adcode,false,obj.name,obj.level,obj);
    //     })
    // }
    initGeoJsonDataByCode(adcode,function(result){
        // console.log(result,"---------")
        getForeignInfoByName(level,name,function(datas){
            // drawCountry(result,{});
            drawCountry(result,datas,name=="中国");
            if(name=="中国"){
                render.control.minDistance = 5000;
                render.viewAnimate({target:{x:-11565.974659092411,y:1.5049999999999955,z:3933.2666557392763},position:{x:-11565.974729472986,y:5017.563704732928,z:3933.2616399513704},time:500});
                // $(".jdx").show();
                $(".jdx").css({display:"block",filter:"drop-shadow(0px 0px 6px #2e72d7)"});
            }else if(name=="美国"){
                render.viewAnimate({target:{x:12158.639723169837,y:0.005000000301831134,z:4133.959593025788},position:{x:12247.14067011556,y:6583.570409193801,z:11.892295720014772},time:500});
            }
            if(render.currCountrysModel.level=="city" && name!="江苏省"){
                initDemoInfoByName(name,"","province","施工");
            }else if(["上海市","重庆市","天津市","北京市","江苏省"].indexOf(render.currCityName) > -1 && render.currCountrysModel.level == "district"){
                initDemoInfoByName(name,"","city","施工");
            }
        })
    })
}
function outCountry(){
    if(render.currCountrysModel.level=="earth" || render.isOutCountry) return;
    render.isOutCountry = true;
    setTimeout(() => {
        render.isOutCountry = false;
    }, 2000);
    clearModels();
    if(render.currCountrysModel.level=="province"){
        render.currCountrysModel.level = "earth";
        if(render.inCountryType=="earth"){
            changeModel(render.inCountryType);
        }else{
            render.earthObject.visible = true;
            render.control.minDistance = 8000;
            render.control.enableRotate = false;
            getForeignInfoByName("earth","",function(){
                showCountryMeshs("plan");
            })
            render.viewAnimate({target:{x:-1505.9191539023175,y:-4.1175299284803975e-14,z:2124.0203267875154},position:{x:-1505.919263556004,y:20278.34552539471,z:2124.0000455859363}});
        }
    }else{
        let level = getPrevLevel();
        let name = render.countrys[render.countrys.length - 2];
        let currName = render.countrys[render.countrys.length - 1];
        if(["上海市","北京市","重庆市","天津市"].indexOf(currName)>-1){
            level = getTsCityLevel(currName,level);
            level = getPrevLevel(level);
        }
        window.parent.postMessage({eve:"clickTitle",title:name,level:level},"*");
        render.currCountrysModel.level = level;
        clickCountryName(render.currCountrysModel.acroutes[render.currCountrysModel.acroutes.length-1],true,name,level);
    }
    if(render.countrys.length>0)render.countrys.splice(render.countrys.length-1,1);
}
//获取直辖市的层级
function getTsCityLevel(name,level){
    if(["上海市","北京市","重庆市","天津市"].indexOf(name)>-1){
        return "city";
    }else{
        return level;
    }
}
function getPrevLevel(level){
    let result = "";
    if(!level) level = render.currCountrysModel.level;
    switch (level) {
        case "district":
            result = "province"
            break;
        case "city":
            result = "country"
            break;
        case "county":
            result = "city"
            break;
        default:
            break;
    }
    return result;
}
//根据code获取边界数据
function initGeoJsonDataByCode(code,callback){
    $.ajax({ 
        type : "GET", //提交方式 
        url : `https://geo.datav.aliyun.com/areas_v3/bound/${code}.json`,
        success : function(response) {//返回数据根据结果进行相应的处理 
            // if(callback) callback(response);
            let features = getJsonFeatures(response.features);
            let result = {outF:features};
            $.ajax({ 
                type : "GET", //提交方式 
                url : `https://geo.datav.aliyun.com/areas_v3/bound/${code}_full.json`,
                success : function(response) {//返回数据根据结果进行相应的处理 
                    result.inF = getJsonFeatures(response.features);
                    if(callback) callback(result);
                },
                error:function(){
                    result.inF = [];
                    if(callback) callback(result);
                }
           })
        }
   })
}

//获取全球项目数据统计
function getForeignInfoByName(type,name,callback){
    //district   区
    type = getTsCityLevel(name,type);
    let url = "";
    if(type=="earth"){      //全球
        // let datas = {"code": 0,"data": [{"尼泊尔": "1","赞比亚": "1","沙特阿拉伯": "5","博茨瓦纳": "4","毛里求斯": "11","南苏丹": "2","乌干达": "2","刚果共和国（刚果（布）": "3","安哥拉": "2","莫桑比克": "1","埃塞俄比亚": "4","帕劳": "1","亚美尼亚": "1","加纳": "1","中国": "514","津巴布韦": "2","蒙古": "1","纳米比亚": "3"}],"msg": ""};
        // render.foreignInfoEarth = datas.data[0];
        // if(callback) callback(datas.data[0]);
        url = "/admin-api/globalManage/zjmanage/largescreen/getForeignInfoEarth";
    }else if(type=="country"){      //国家
        // let datas = {"code": 0,"data": [{"Sofala": "3","Abo Dhabi": "4","贵州省": "1","East Shewa": "3","上海市": "12","广东省": "4","湖北省": "4","湖南省": "2","Central": "2","安徽省": "5","四川省": "3","Western": "4","North Western": "2","Black River": "1","Kerewan": "1","江苏省": "160","North": "3","Pool": "1","Bie": "2","Meru": "2","河北省": "1","广西壮族自治区": "2","Southern Highlands": "1","West": "2","江西省": "2","重庆市": "1","Port Louis": "1","北京市": "7","Midlands": "3","Grand Port": "7","山东省": "9","陕西省": "3","浙江省": "5","Greater Accra": "1","Lakes": "5","天津市": "1","Savanne": "1","Mashonaland East": "1","Al Anbar": "9","山西省": "1"}],"msg": ""};
        // if(callback) callback(datas.data[0]);
        url = "/admin-api/globalManage/zjmanage/largescreen/getInnerProviceInfo";
    }else if(type=="province"){     //省份
        // let datas = {"code": 0,"data": [{"苏州市": "32","徐州市": "24","宿迁市": "3","盐城市": "1","扬州市": "13","淮安市": "6","南京市": "60","泰州市": "4","常州市": "4","无锡市": "5","南通市": "2","江苏省": "160","镇江市": "2","连云港市": "4"}],"msg": ""};
        // if(callback) callback(datas.data[0]);
        url = "/admin-api/globalManage/zjmanage/largescreen/getInnerProviceCityInfo?province="+name;
    }else if(type=="city"){     //市
        // let datas = {"code": 0,"data": [{"溧水区": "1","鼓楼区": "3","六合区": "1","秦淮区": "3","玄武区": "35","浦口区": "2","江宁区": "7","栖霞区": "2","建邺区": "1","雨花台区": "5","南京市": "60"}],"msg": ""};
        // if(callback) callback(datas.data[0]);
        url = "/admin-api/globalManage/zjmanage/largescreen/getInnerProviceCityDistrictInfo?city="+name;
    }else{
        if(callback) callback();
    }
    httpAjax(url,function(res){
        // console.log(res,"---------");
        if(res&&res.data){
            if(type=="earth") render.foreignInfoEarth = res.data[0];
            if(callback) callback(res.data[0]);
        }else{
            if(type=="earth") render.foreignInfoEarth = {};
            if(callback) callback({});
        }
    });
}
function changeDemoInfoByProjectType(type){
    clearDemoInfosModel();
    initDemoInfoByName(render.currDemoInfoConfig.name,render.currDemoInfoConfig.isGW,render.currDemoInfoConfig.type,type);
}
function initDemoInfoByName(name,isGw,type,projectType){
    render.control.minDistance = 5;
    window.parent.postMessage({eve:"initDemoInfo"},"*");
    let showName = 0;
    let images = {},total = 0;
    let allImageUrls = [];
    render.currDemoInfoConfig = {name,isGw,type,projectType};
    getDemoInfos(name,isGw,function(datas){
        let coordinates = {};
        for(let i=0;i<datas.length;i++){
            let center = new THREE.Vector3(0,0,0);
            if(datas[i]["项目经度"]&&datas[i]["项目纬度"]){
                let key = datas[i]["项目经度"]+"_"+datas[i]["项目纬度"];
                center = lonlatToPlanVector3(datas[i]["项目经度"],datas[i]["项目纬度"],render.earthSize);
                if(coordinates[key]){
                    center.x -= Math.random();
                    center.z -= Math.random();
                }
                if(!coordinates[key]) coordinates[key] = center;
            }else{
                if(render.currCountrysModel.currPolys){
                    let lnglat = getRandomPointInPoly(render.currCountrysModel.currPolys);
                    center = lonlatToPlanVector3(lnglat[0],lnglat[1],render.earthSize);
                }else{
                    let box = getObjectBox(render.currCountrysModel);
                    center = box.getCenter(center);
                }
            }
            let name = datas[i]["项目名称"] || "";
            let jcName = datas[i]["项目简称"] || name;
            let id = datas[i]["ID"] || datas[i]["项目ID"] || "";
            showName++;
            // render.setInfoWindow(`<div class="demoBg" code="${id}" onclick="clickDemo(this)" onTouchstart="touchePointEvent()" onTouchend="clickDemo(this)" type="project" isGw="${isGw}" titleName="${name}" title="${name}"><div class="demoBg_title">${showName}</div><div class="demoBg_zz"></div></div>`,{x:center.x,y:0,z:center.z},{name:name});
            // if(render.infoWindos[name]) render.infoWindos[name].isAvoid = true;
            let renderingList = datas[i].renderingList;
            let img = "img/xm.png";
            if(renderingList){
                renderingList = JSON.parse(renderingList);
                if(renderingList[0].downloadUrl) img = renderingList[0].downloadUrl;
            }
            if(isGw){
                let xmImages = datas[i]["项目效果图"];
                if(xmImages){
                    img = xmImages.split(",")[0];
                    allImageUrls.push(img);
                }else{
                    xmImages = datas[i]["项目照片"] || "";
                    if(xmImages){
                        img = xmImages.split(",")[0];
                        allImageUrls.push(img);
                    }
                }
            }
            let city = datas[i]["市"];
            if(isGw){
                city = datas[i]["省份"];
            }else{
                if(type=="city") city = datas[i]["区"];
            }
            if(!city) city = "";
            if(!images[city]) images[city] = [];
            images[city].push([jcName,img,center,id,"project",city]);
            total++;
        }
        if(isGw){
            getYdInfos(name,isGw,function(datas){
                // console.log(datas,"----------")
                let coordinates = {};
                for(let i=0;i<datas.length;i++){
                    let center = new THREE.Vector3(0,0,0);
                    if(datas[i]["营地经度"]&&datas[i]["营地纬度"]){
                        center = lonlatToPlanVector3(datas[i]["营地经度"],datas[i]["营地纬度"],render.earthSize);
                        if(coordinates[key]){
                            center.x -= Math.random();
                            center.z -= Math.random();
                        }
                        if(!coordinates[key]) coordinates[key] = center;
                    }else{
                        if(render.currCountrysModel.currPolys){
                            let lnglat = getRandomPointInPoly(render.currCountrysModel.currPolys);
                            center = lonlatToPlanVector3(lnglat[0],lnglat[1],render.earthSize);
                        }else{
                            let box = getObjectBox(render.currCountrysModel);
                            center = box.getCenter(center);
                        }
                    }
                    let name = datas[i]["营地名称"] || "";
                    let id = datas[i]["营地id"] || "";
                    showName++;
                    // render.setInfoWindow(`<div class="demoBg" code="${id}" onclick="clickDemo(this)" onTouchstart="touchePointEvent()" onTouchend="clickDemo(this)" type="yd" isGw="${isGw}" titleName="${name}" title="${name}"><div class="demoBg_title">${showName}</div><div class="demoBg_zz"></div></div>`,{x:center.x,y:0,z:center.z},{name:name});
                    // if(render.infoWindos[name]) render.infoWindos[name].isAvoid = true;
                    // images.push([name,"img/xm.png",center,id,"yd",name]);
                    let city = datas[i]["省份"];
                    if(!city) city = "";
                    if(!images[city]) images[city] = [];
                    let img = "img/xm.png";
                    let ydImages = datas[i]["营地照片"] || "";
                    if(ydImages){
                        img = ydImages.split(",")[0];
                        allImageUrls.push(img);
                    }
                    images[city].push([name,img,center,id,"yd",city]);
                    total++;
                }
                if(isGw){
                    initXmydImage(images,total);
                    getProjectPhotoUrls(allImageUrls);
                }
            })
        }else{
            if(total>0)initXmydImage(images,total);
        }
    },type,projectType)
}
function initXmydImage(images,allTotal){
    let total = Object.keys(images).length;
    if(total<13){
        let num = 13 - total;
        for(let i=0;i<num;i++){
            let key = Object.keys(images)[0];
            if(key.endsWith("_new")) continue;
            images[`${key}_${i}_new`] = [images[key][0]];
            images[key].splice(0,1);
            if(images[key].length<=0){
                delete images[key];
                num ++;
            }
        }
    }
    // console.log(images,"-----------")
    let isChange = "";
    if(allTotal<13) isChange = "true";
    // let config = {minY:render.currBoxConfig.minY + }
    // console.log(images,"-------------")
    let num = 3;
    const scale = render.currBoxConfig.h / 1500;
    const positions = [];
    let w = Math.max(render.currBoxConfig.w / 2,render.currBoxConfig.h / 2);
    // const step = render.currBoxConfig.h / (num - 1);
    const step1 = render.currBoxConfig.h / (num - 0.5);
    const cssLabelW = scale * 960;
    const cssLabelH = scale * 430;
    for(let i=0;i<num;i++){
        positions.push([render.currBoxConfig.minX - cssLabelW / 2,0,step1 * i + render.currBoxConfig.minY + step1 * 0.2,0]);
        positions.push([render.currBoxConfig.maxX + cssLabelW / 2,0,step1 * i + render.currBoxConfig.minY + step1 * 0.2,1]);

        positions.push([render.currBoxConfig.minX - cssLabelW * 1.8,0,render.currBoxConfig.maxY - step1 * i,0]);
        positions.push([render.currBoxConfig.maxX + cssLabelW * 1.8,0,render.currBoxConfig.maxY - step1 * i,1]);
    }
    // console.log(positions,"-------------",images)
    render.currXmydDatas = {};
    render.currXmydConfigDatas = {};
    render.currXmydLines = {};
    let maxX = render.currBoxConfig.maxX;
    let maxY = render.currBoxConfig.maxY;
    let minY = render.currBoxConfig.minY;
    let minX = render.currBoxConfig.minX;
    let prevAngle = null;
    for(let key in images){
        let minDis = Infinity;let index = -1;
        // let city = images[key][5];
        if(key.endsWith("_new")){
            city = key.split("_")[0];
        }else{
            city = key;
        }
        // console.log(city,"-----------")
        let countryCoord = render.currCountryNames[city];
        if(!countryCoord){
            for(let key in render.currCountryNames){
                let _key = key;
                if(key.endsWith("省")){
                    _key = key.replace("省","")
                }else if(key.endsWith("区")){
                    _key = key.replace("区","")
                }else if(key.endsWith("州")){
                    _key = key.replace("州","")
                }
                // let _key = key.replace("省","").replace("区","").replace("州","");
                if(city.indexOf(_key)>-1 || _key.indexOf(city)>-1){
                    countryCoord = render.currCountryNames[key];
                    break;
                }
            }
        }
        if(!city || city=="undefined"){
            let _key = Object.keys(render.currCountryNames)[0];
            countryCoord = render.currCountryNames[_key];
        }
        if(!countryCoord) continue;
        if(countryCoord.mesh) countryCoord.mesh.visible = true;
        let datas = images[key];
        for(let i=0;i<positions.length;i++){
            if(positions[i][4]) continue;
            let p = new THREE.Vector3(positions[i][0],positions[i][1],positions[i][2]);
            let distance = p.distanceTo(countryCoord);
            if(minDis > distance && positions[i][3] != prevAngle){
                // nearPoint = p;
                index = i;
                minDis = distance;
            }
        }
        if(positions[index]){
            positions[index][4] = 1;
            prevAngle = positions[index][3];
            let str = ``,img = "img/xm.png";
            let p = new THREE.Vector3(positions[index][0],positions[index][1],positions[index][2]);
            let lines = [];
            for(let i=0;i<datas.length;i++){
                render.currXmydDatas[`${key}_${i}`] = datas[i];
                render.currXmydConfigDatas[`${key}_${i}`] = {
                    p,cssLabelW,cssLabelH,isLeft:positions[index][3],countryCoord
                }
                let name = datas[i][0];
                if(name.length > 6){
                    if(isChange){
                        name = name.substring(0,10) + "...";
                    }else{
                        name = name.substring(0,6) + "...";
                    }
                }
                if(i==0){
                    str += `<div class="xmydInfo_title" onclick="xmydInfoClick(this)" title="${datas[i][0]}" onmouseover="xmydInfoMouseOver(this)"  onmouseout="xmydInfoMouseOut(this)" type="${datas[i][4]}" city="${city}" code="${key}" index="${i}" img="${datas[i][1]}" sel='ok'><img src="img/xm/cir.png"/>${name}</div>`;
                    img = datas[i][1];
                }else{
                    str += `<div class="xmydInfo_title" onclick="xmydInfoClick(this)" title="${datas[i][0]}" onmouseover="xmydInfoMouseOver(this)"  onmouseout="xmydInfoMouseOut(this)" type="${datas[i][4]}" city="${city}" code="${key}" index="${i}" img="${datas[i][1]}"><img src="img/xm/cir.png"/>${name}</div>`
                }
                // let lines = [];
                // if(positions[index][3]){
                //     lines = [
                //         new THREE.Vector3(p.x - cssLabelW / 2 + cssLabelW * 0.05,p.y,p.z + cssLabelH / 2 - cssLabelH * (0.1 + i * 0.2)),//0.3
                //         new THREE.Vector3(Math.max(p.x - cssLabelW / 2 - cssLabelW * 0.1,render.currBoxConfig.maxX),p.y,p.z + cssLabelH / 2 - cssLabelH * (0.1 + i * 0.2)),//0.3
                //         new THREE.Vector3(Math.max(p.x - cssLabelW / 2 - cssLabelW * 0.1,render.currBoxConfig.maxX),p.y,p.z + cssLabelH / 2 - cssLabelH * 0.1),
                //         new THREE.Vector3(render.currBoxConfig.maxX ,p.y,p.z + cssLabelH / 2 - cssLabelH * 0.1),
                //         new THREE.Vector3(countryCoord.x ,countryCoord.y,p.z + cssLabelH / 2 - cssLabelH * 0.1),
                //         countryCoord
                //     ];
                // }else{
                //     lines = [
                //         new THREE.Vector3(p.x + cssLabelW / 2 - cssLabelW * 0.05,p.y,p.z + cssLabelH / 2 - cssLabelH * (0.1 + i * 0.2)), //0.3
                //         new THREE.Vector3(Math.min(p.x + cssLabelW / 2 + cssLabelW * 0.1,render.currBoxConfig.minX),p.y,p.z + cssLabelH / 2 - cssLabelH * (0.1 + i * 0.2)),//0.3
                //         new THREE.Vector3(Math.min(p.x + cssLabelW / 2 + cssLabelW * 0.1,render.currBoxConfig.minX),p.y,p.z + cssLabelH / 2 - cssLabelH * 0.1),
                //         new THREE.Vector3(render.currBoxConfig.minX ,p.y,p.z + cssLabelH / 2 - cssLabelH * 0.1),
                //         new THREE.Vector3(countryCoord.x ,countryCoord.y,p.z + cssLabelH / 2 - cssLabelH * 0.1),
                //         countryCoord
                //     ];
                // }
                // let joinLine = initJoinLine(lines,1,scale);
                // joinLine.renderOrder = 9999;
                // render.currCountrysModel[`${key}_${i}_line`] = joinLine;
            }
            if(positions[index][3]){
                html = `<div class="xmydInfo"><div class="xmydInfo_imgBg" isChange="${isChange}${positions[index][3]}"><img src="${img}"/></div><div class="xmydInfo_title_list" isChange="${isChange}${positions[index][3]}" scale="${scale}" code="${key}" city="${city}" onmouseover="xmydInfo_title_listMouseOver(this)"  onmouseout="xmydInfo_title_listMouseOut(this)">
                    ${str}
                </div><div>`;
                lines.push(
                    new THREE.Vector3(p.x - cssLabelW / 2 + cssLabelW * 0.05,p.y,p.z + cssLabelH / 2 - cssLabelH * 0.1),//0.3
                    new THREE.Vector3(Math.max(p.x - cssLabelW / 2 - cssLabelW * 0.1,render.currBoxConfig.maxX),p.y,p.z + cssLabelH / 2 - cssLabelH * 0.1),//0.3
                    new THREE.Vector3(Math.max(p.x - cssLabelW / 2 - cssLabelW * 0.1,render.currBoxConfig.maxX),p.y,p.z + cssLabelH / 2 - cssLabelH * 0.1),
                    new THREE.Vector3(render.currBoxConfig.maxX ,p.y,p.z + cssLabelH / 2 - cssLabelH * 0.1),
                    new THREE.Vector3(countryCoord.x ,countryCoord.y,p.z + cssLabelH / 2 - cssLabelH * 0.1),
                    new THREE.Vector3(countryCoord.x,countryCoord.y,countryCoord.z)
                );
            }else{
                html = `<div class="xmydInfo"><div class="xmydInfo_title_list" isChange="${isChange}${positions[index][3]}" code="${key}" city="${city}" scale="${scale}" onmouseover="xmydInfo_title_listMouseOver(this)"  onmouseout="xmydInfo_title_listMouseOut(this)">
                    ${str}
                </div><div class="xmydInfo_imgBg" isChange="${isChange}${positions[index][3]}"><img src="${img}"/></div><div>`;
                lines.push(
                    new THREE.Vector3(p.x + cssLabelW / 2 - cssLabelW * 0.05,p.y,p.z + cssLabelH / 2 - cssLabelH * 0.1), //0.3
                    new THREE.Vector3(Math.min(p.x + cssLabelW / 2 + cssLabelW * 0.1,render.currBoxConfig.minX),p.y,p.z + cssLabelH / 2 - cssLabelH * 0.1),//0.3
                    new THREE.Vector3(Math.min(p.x + cssLabelW / 2 + cssLabelW * 0.1,render.currBoxConfig.minX),p.y,p.z + cssLabelH / 2 - cssLabelH * 0.1),
                    new THREE.Vector3(render.currBoxConfig.minX ,p.y,p.z + cssLabelH / 2 - cssLabelH * 0.1),
                    new THREE.Vector3(countryCoord.x ,countryCoord.y,p.z + cssLabelH / 2 - cssLabelH * 0.1),
                    new THREE.Vector3(countryCoord.x,countryCoord.y,countryCoord.z)
                );
            }
            let joinLine = initJoinLine(lines,1,scale);
            joinLine.renderOrder = 9999;
            render.currDemoInfosModel.add(joinLine);
            render.currXmydLines[key] = joinLine;
            let css3DLabel = initCss3DLabel(html);
            css3DLabel.position.set(positions[index][0],positions[index][1],positions[index][2]);
            css3DLabel.scale.set(scale,scale,scale);
            render.currDemoInfosModel.add(css3DLabel);
            maxX = Math.max(maxX,p.x - cssLabelW);
            maxX = Math.max(maxX,p.x + cssLabelW);
            minX = Math.min(minX,p.x - cssLabelW);
            minX = Math.min(minX,p.x + cssLabelW);

            maxY = Math.max(maxY,p.z - cssLabelH);
            maxY = Math.max(maxY,p.z + cssLabelH);
            minY = Math.min(minY,p.z - cssLabelH);
            minY = Math.min(minY,p.z + cssLabelH);
        }
    }
    let len = Math.max((maxX - minX) / 3,maxY - minY);
    setTimeout(() => {
        render.viewAnimate({target:{x:render.currCountrysModel.center.x,y:render.currCountrysModel.center.y,z:render.currCountrysModel.center.z}
            ,position:{x:render.currCountrysModel.center.x,y:len * 1.3,z:render.currCountrysModel.center.z - 0.1},time:500})
    }, 500);
    if(render.xmInfoTimer) clearInterval(render.xmInfoTimer);
    render.xmInfoTimer = setInterval(() => {
        for(let key in images){
            let isOver = $(".xmydInfo_title_list[code='"+key+"']").attr("isOver");
            if(isOver) continue;
            let lists = $(".xmydInfo_title_list[code='"+key+"']").find(".xmydInfo_title");
            let length = lists.length;
            for(let i=0;i<length;i++){
                let sel = $(lists[i]).attr("sel");
                if(sel){
                    $(lists[i]).attr("sel","");
                    let index = i+1;
                    if(index>=length) index = 0;
                    $(lists[index]).attr("sel","ok");
                    let img = $(lists[index]).attr("img");
                    let code = $(lists[index]).attr("code");
                    let index1 = $(lists[index]).attr("index");
                    let top = index1 * 85;
                    $(".xmydInfo_title_list[code='"+code+"']").scrollTop(top);
                    $(".xmydInfo_title_list[code='"+key+"']").parent().find(".xmydInfo_imgBg").find("img").attr("src",img);
                    updateXmydInfo(code,index1);
                    break;
                }
            }
        }
    }, 5000);
}
function xmydInfo_title_listMouseOver(){
    render.control.enableZoom = false;
}
function xmydInfo_title_listMouseOut(){
    render.control.enableZoom = true;
}
function xmydInfoClick(obj){
    let code = $(obj).attr("code");
    let index = $(obj).attr("index");
    let data = render.currXmydDatas[`${code}_${index}`];
    if(data){
        window.parent.postMessage({eve:"clickDemo",type:data[4],code:data[3],name:data[0]},"*");
    }
}
function updateXmydInfo(code,index){
    if(render.currXmydLines[code]){
        let datas = render.currXmydConfigDatas[`${code}_${index}`];
        if(datas){
            let lines = [];
            let top = $(".xmydInfo_title_list[code='"+code+"']").scrollTop() || 0;
            let scale = $(".xmydInfo_title_list[code='"+code+"']").attr("scale");
            top = top * scale;
            if(datas.isLeft){
                lines.push(
                    new THREE.Vector3(datas.p.x - datas.cssLabelW / 2 + datas.cssLabelW * 0.05,datas.p.y,datas.p.z + datas.cssLabelH / 2 - datas.cssLabelH * (0.1 + index * 0.2) + top),//0.3
                    new THREE.Vector3(Math.max(datas.p.x - datas.cssLabelW / 2 - datas.cssLabelW * 0.1,render.currBoxConfig.maxX),datas.p.y,datas.p.z + datas.cssLabelH / 2 - datas.cssLabelH * (0.1 + index * 0.2) + top),//0.3
                    new THREE.Vector3(Math.max(datas.p.x - datas.cssLabelW / 2 - datas.cssLabelW * 0.1,render.currBoxConfig.maxX),datas.p.y,datas.p.z + datas.cssLabelH / 2 - datas.cssLabelH * 0.1),
                    new THREE.Vector3(render.currBoxConfig.maxX ,datas.p.y,datas.p.z + datas.cssLabelH / 2 - datas.cssLabelH * 0.1),
                    new THREE.Vector3(datas.countryCoord.x ,datas.countryCoord.y,datas.p.z + datas.cssLabelH / 2 - datas.cssLabelH * 0.1),
                    new THREE.Vector3(datas.countryCoord.x,datas.countryCoord.y,datas.countryCoord.z)
                );
            }else{
                lines.push(
                    new THREE.Vector3(datas.p.x + datas.cssLabelW / 2 - datas.cssLabelW * 0.05,datas.p.y,datas.p.z + datas.cssLabelH / 2 - datas.cssLabelH * (0.1 + index * 0.2) + top), //0.3
                    new THREE.Vector3(Math.min(datas.p.x + datas.cssLabelW / 2 + datas.cssLabelW * 0.1,render.currBoxConfig.minX),datas.p.y,datas.p.z + datas.cssLabelH / 2 - datas.cssLabelH * (0.1 + index * 0.2) + top),//0.3
                    new THREE.Vector3(Math.min(datas.p.x + datas.cssLabelW / 2 + datas.cssLabelW * 0.1,render.currBoxConfig.minX),datas.p.y,datas.p.z + datas.cssLabelH / 2 - datas.cssLabelH * 0.1),
                    new THREE.Vector3(render.currBoxConfig.minX ,datas.p.y,datas.p.z + datas.cssLabelH / 2 - datas.cssLabelH * 0.1),
                    new THREE.Vector3(datas.countryCoord.x ,datas.countryCoord.y,datas.p.z + datas.cssLabelH / 2 - datas.cssLabelH * 0.1),
                    new THREE.Vector3(datas.countryCoord.x,datas.countryCoord.y,datas.countryCoord.z)
                );
            }
            let geo = creatPath(lines,creatArrowImg(136,61,30,"rgba(0,0,0,0)","#05b4d8"),scale || 0.1,true);
            render.currXmydLines[code].geometry.setAttribute("position",geo.getAttribute("position"));
            render.currXmydLines[code].geometry.setAttribute("normal",geo.getAttribute("normal"));
            render.currXmydLines[code].geometry.setAttribute("uv",geo.getAttribute("uv"));
        }
    }
}
function xmydInfoMouseOver(obj){
    let code = $(obj).attr("code");
    let index = $(obj).attr("index");
    let img = $(obj).attr("img");
    // let oldCode = $(obj).parent().find(".xmydInfo_title[sel='ok']").attr("code");
    // let oldIndex = $(obj).parent().find(".xmydInfo_title[sel='ok']").attr("index");
    $(obj).parent().find(".xmydInfo_title").attr("sel","");
    $(obj).attr("sel","ok");
    $(".xmydInfo_title_list[code='"+code+"']").attr("isOver","ok");
    $(obj).parent().parent().find(".xmydInfo_imgBg").find("img").attr("src",img);
    if(render.currXmydLines[code]){
        let datas = render.currXmydConfigDatas[`${code}_${index}`];
        if(datas){
            let lines = [];
            let top = $(".xmydInfo_title_list[code='"+code+"']").scrollTop() || 0;
            let scale = $(".xmydInfo_title_list[code='"+code+"']").attr("scale");
            top = top * scale;
            if(datas.isLeft){
                lines.push(
                    new THREE.Vector3(datas.p.x - datas.cssLabelW / 2 + datas.cssLabelW * 0.05,datas.p.y,datas.p.z + datas.cssLabelH / 2 - datas.cssLabelH * (0.1 + index * 0.2) + top),//0.3
                    new THREE.Vector3(Math.max(datas.p.x - datas.cssLabelW / 2 - datas.cssLabelW * 0.1,render.currBoxConfig.maxX),datas.p.y,datas.p.z + datas.cssLabelH / 2 - datas.cssLabelH * (0.1 + index * 0.2) + top),//0.3
                    new THREE.Vector3(Math.max(datas.p.x - datas.cssLabelW / 2 - datas.cssLabelW * 0.1,render.currBoxConfig.maxX),datas.p.y,datas.p.z + datas.cssLabelH / 2 - datas.cssLabelH * 0.1),
                    new THREE.Vector3(render.currBoxConfig.maxX ,datas.p.y,datas.p.z + datas.cssLabelH / 2 - datas.cssLabelH * 0.1),
                    new THREE.Vector3(datas.countryCoord.x ,datas.countryCoord.y,datas.p.z + datas.cssLabelH / 2 - datas.cssLabelH * 0.1),
                    new THREE.Vector3(datas.countryCoord.x,datas.countryCoord.y,datas.countryCoord.z)
                );
            }else{
                lines.push(
                    new THREE.Vector3(datas.p.x + datas.cssLabelW / 2 - datas.cssLabelW * 0.05,datas.p.y,datas.p.z + datas.cssLabelH / 2 - datas.cssLabelH * (0.1 + index * 0.2) + top), //0.3
                    new THREE.Vector3(Math.min(datas.p.x + datas.cssLabelW / 2 + datas.cssLabelW * 0.1,render.currBoxConfig.minX),datas.p.y,datas.p.z + datas.cssLabelH / 2 - datas.cssLabelH * (0.1 + index * 0.2) + top),//0.3
                    new THREE.Vector3(Math.min(datas.p.x + datas.cssLabelW / 2 + datas.cssLabelW * 0.1,render.currBoxConfig.minX),datas.p.y,datas.p.z + datas.cssLabelH / 2 - datas.cssLabelH * 0.1),
                    new THREE.Vector3(render.currBoxConfig.minX ,datas.p.y,datas.p.z + datas.cssLabelH / 2 - datas.cssLabelH * 0.1),
                    new THREE.Vector3(datas.countryCoord.x ,datas.countryCoord.y,datas.p.z + datas.cssLabelH / 2 - datas.cssLabelH * 0.1),
                    new THREE.Vector3(datas.countryCoord.x,datas.countryCoord.y,datas.countryCoord.z)
                );
            }
            let geo = creatPath(lines,creatArrowImg(136,61,30,"rgba(0,0,0,0)","#05b4d8"),scale || 0.1,true);
            render.currXmydLines[code].geometry.setAttribute("position",geo.getAttribute("position"));
            render.currXmydLines[code].geometry.setAttribute("normal",geo.getAttribute("normal"));
            render.currXmydLines[code].geometry.setAttribute("uv",geo.getAttribute("uv"));
        }
    }
    // if(render.currCountrysModel[`${oldCode}_${oldIndex}_line`]){
    //     render.currCountrysModel.remove(render.currCountrysModel[`${oldCode}_${oldIndex}_line`]);
    //     // if(!render.currCountrysModel[`${oldCode}_${oldIndex}_line`].oldMaterial) render.currCountrysModel[`${oldCode}_${oldIndex}_line`].material = render.currCountrysModel[`${oldCode}_${oldIndex}_line`].oldMaterial;
    // }
    if(render.currXmydLines[code]){
        // render.currCountrysModel.add(render.currXmydLines[code]);
        if(!render.currXmydLines[code].oldMaterial) render.currXmydLines[code].oldMaterial = render.currXmydLines[code].material;
        let rx = render.currXmydLines[code].material.map.repeat.x / 1.5;
        render.hilightPathMaterial.map.repeat.x = Math.max(rx,5);
        render.currXmydLines[code].material = render.hilightPathMaterial;
    }
}
function xmydInfoMouseOut(obj){
    let code = $(obj).attr("code");
    let index = $(obj).attr("index");
    $(".xmydInfo_title_list[code='"+code+"']").attr("isOver","");
    if(render.currXmydLines[code]){
        if(render.currXmydLines[code].oldMaterial) render.currXmydLines[code].material = render.currXmydLines[code].oldMaterial;
    }
}
function initXmydImage_(images){
    let num = Math.ceil(images.length / 2);
    const scale = render.currBoxConfig.h / 1500;
    const positions = [];
    let w = Math.max(render.currBoxConfig.w / 2,render.currBoxConfig.h / 2);
    for(let j=0; j < num; j++){
        if(!images[j]) break;
        if(num < 2){
            positions.push([render.currBoxConfig.minX - w * Math.random() * 0.1,0,render.currBoxConfig.h / 2 * (j + 1) + render.currBoxConfig.minY]);
            positions.push([render.currBoxConfig.maxX + w * Math.random() * 0.1,0,render.currBoxConfig.h / 2 * (j + 1) + render.currBoxConfig.minY]);
        }else if(num < 4){
            positions.push([render.currBoxConfig.minX - w * Math.random(),0,render.currBoxConfig.h / (num - 1) * j + render.currBoxConfig.minY]);
            positions.push([render.currBoxConfig.maxX + w * Math.random(),0,render.currBoxConfig.h / (num - 1) * j + render.currBoxConfig.minY]);
        }else{
            if(j%2==0){
                positions.push([render.currBoxConfig.minX - ( w - Math.random() * w),0,render.currBoxConfig.h / (num - 1) * j + render.currBoxConfig.minY]);
                positions.push([render.currBoxConfig.maxX + ( w - Math.random() * w),0,render.currBoxConfig.h / (num - 1) * j + render.currBoxConfig.minY]);
            }else{
                positions.push([render.currBoxConfig.minX - (w + Math.random() * w),0,render.currBoxConfig.h / (num - 1) * j + render.currBoxConfig.minY]);
                positions.push([render.currBoxConfig.maxX + (w + Math.random() * w),0,render.currBoxConfig.h / (num - 1) * j + render.currBoxConfig.minY]);
            }
        }
    }
    for(let i=0;i<images.length;i++){
        let minDis = Infinity;
        let index = 0;
        for(let j=0;j<positions.length;j++){
            if(positions[j].isUse) continue;
            let distance = new THREE.Vector3(images[i][2].x,images[i][2].y,images[i][2].z).distanceTo(new THREE.Vector3(positions[j][0],positions[j][1],positions[j][2]));
            if(minDis > distance){
                minDis = distance;
                index = j;
            }
        }
        positions[index].isUse = true;
        // console.log(positions[index],index);
        // const html = `<div class="xmydInfo" style="margin-left: 200px;"><div class="xmydInfo_title">${images[i][0]}</div><img src="${images[i][1]}"/><div>`
        let showName = images[i][0];
        if(showName.length > 16) showName = showName.substring(0,16) + "...";
        const html = `<div class="xmydInfo" title="${images[i][0]}"><div class="xmydInfo_title">${showName}</div><div class="xmydInfo_imgBg"><img src="${images[i][1]}"/></div><div>`
        let css3DLabel = initCss3DLabel(html);
        css3DLabel.position.set(positions[index][0],positions[index][1],positions[index][2]);
        // css3DLabel.position.set(render.currBoxConfig.minX,0,render.currBoxConfig.h / (num - 1) * i + render.currBoxConfig.minY);
        css3DLabel.scale.set(scale,scale,scale);
        render.currCountrysModel.add(css3DLabel);
        let joinLine = initJoinLine([css3DLabel.position,new THREE.Vector3(images[i][2].x,css3DLabel.position.y,css3DLabel.position.z),new THREE.Vector3(images[i][2].x,images[i][2].y,images[i][2].z)],render.currBoxConfig.w / 5,scale);
        joinLine.renderOrder = 9999;
        render.currCountrysModel.add(joinLine);
    }
}
function highLigtDemo(name){
    if(render.highLigtDemoInfo){
        $(render.highLigtDemoInfo.element).find(".demoBg").css({zoom:1,filter:"none"});
        render.highLigtDemoInfo.renderOrder = 0;
    }
    if(name&&render.infoWindos[name]){
        render.highLigtDemoInfo = render.infoWindos[name];
        $(render.infoWindos[name].element).find(".demoBg").css({zoom:1.5,filter: "brightness(1.5)"})
        render.infoWindos[name].renderOrder = 999;
    }
}
function getRandomPointInPoly(points){
    // 创建多边形
    const polygon = turf.polygon([points]);
    // 获取多边形的边界框
    const bbox = turf.bbox(polygon);
    const [minX, minY, maxX, maxY] = bbox;
    
    // 生成一个在多边形内的随机点
    function getRandomPointInPolygon(poly) {
        let pt;
        do {
            const x = Math.random() * (maxX - minX) + minX;
            const y = Math.random() * (maxY - minY) + minY;
            pt = turf.point([x, y]);
        } while (!turf.booleanPointInPolygon(pt, poly)); // 检查点是否在多边形内
        return pt;
    }
    
    const randomPoint = getRandomPointInPolygon(polygon)
    return randomPoint.geometry.coordinates;
}
//获取项目信息
function getDemoInfos(name,isGw,callback,type,projectType){
    let url = `/admin-api/globalManage/zjmanage/largescreen/getXmjbxx?city=${name}&projectType=${projectType}`; //city  -- district
    if(type=="province") url = `/admin-api/globalManage/zjmanage/largescreen/getXmjbxx?province=${name}&projectType=${projectType}`; //city  -- district
    if(isGw) url = `/admin-api/globalManage/zjmanage/largescreen/getXmxxV2?gb=${name}`;
    httpAjax(url,function(res){
        // console.log(res,"---------");
        if(callback) callback(res.data);
    });
}
//获取营地信息
function getYdInfos(name,isGW,callback){
    let url = "/admin-api/globalManage/zjmanage/largescreen/getYdxxV2?gb="+name;
    if(isGW) url = "/admin-api/globalManage/zjmanage/largescreen/getYdxxV2?gb="+name;
    httpAjax(url,function(res){
        // console.log(res,"---------");
        if(callback) callback(res.data);
    });
}
function clickDemo(obj){
    if(event&&event.isTrusted&&event.changedTouches){
        if(Math.abs(event.changedTouches[0].clientX - render.touchePoint.x)>2 || Math.abs(event.changedTouches[0].clientY - render.touchePoint.y)>2) return;
    }
    let code = $(obj).attr("code");
    let type = $(obj).attr("type");
    let name = $(obj).attr("titleName");
    let isGw = $(obj).attr("isGw");
    // console.log(code,"---------",isGw)
    window.parent.postMessage({eve:"clickDemo",type:type,code:code,name:name,isGw:isGw},"*");
}

let httpUrl = "http://222.190.120.19:8088";
httpUrl = "http://192.168.15.74:31822";
function httpAjax(url,callback){
    $.ajax({
        // url: httpUrl+url,
        url: url,
        type: "get",
        headers: {
            "Content-Type": "application/json",
            Authorization: 'Bearer ' + render.token,
            "tenant-id": "1"
        },
        success: function (res) {
            if(callback)callback(res);
        },
        error:function(res){
            if(callback)callback();
        }   
    })
}
function httpAjaxPost(url,data,callback){
    $.ajax({
        // url: httpUrl+url,
        url: url,
        type: "POST",
        data:JSON.stringify(data),
        headers: {
            "Content-Type": "application/json",
            // Authorization: 'Bearer ' + render.token,
            // "tenant-id":1
        },
        success: function (res) {
            if(callback)callback(res);
        },
        error:function(res){
            if(callback)callback();
        }   
    })
}

function saveJSON (data, filename){
    if(!data) {
        // alert('保存的数据为空');
        return;
    }
    if(!filename) 
        filename = 'json.json'
    if(typeof data === 'object'){
        data = JSON.stringify(data, undefined, 0)
    }
    var blob = new Blob([data], {type: 'text/json'}),
    e = document.createEvent('MouseEvents'),
    a = document.createElement('a')
    a.download = filename
    a.href = window.URL.createObjectURL(blob)
    a.dataset.downloadurl = ['text/json', a.download, a.href].join(':')
    e.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null)
    a.dispatchEvent(e)
}

function creatEarthCountyName(name,num,center){
    if(name=="刚果共和国（刚果（布）") name = "刚果（布）";
    else if(name=="刚果共和国（刚果（金）") name = "刚果（金）";
    else if(name=="阿拉伯联合酋长国（阿联酋）") name = "阿联酋";
    let canvas = document.createElement("canvas");
    let ctx = canvas.getContext("2d");
    ctx.font = "bold 30px sans-serif";
    const textW = ctx.measureText(name).width;
    const textW1 = ctx.measureText(num).width;
    const width = Math.max(textW,textW1);
    canvas.width = width + 20;
    canvas.height = 62;
    ctx.fillStyle = "#fff";
    ctx.font = "bold 30px sans-serif";
    ctx.shadowBlur = 2;        // 阴影模糊程度
    ctx.shadowOffsetX = 3;       // 水平方向阴影偏移量
    ctx.shadowOffsetY = 3;       // 垂直方向阴影偏移量
    ctx.shadowColor = '#000'; // 阴影颜色
    ctx.strokeStyle = '#000';
    ctx.strokeText(name,10 + (width - textW) / 2,30);
    ctx.fillText(name,10 + (width - textW) / 2,30);
    ctx.strokeText(num,10 + (width - textW1) / 2,60);
    ctx.fillText(num,10 + (width - textW1) / 2,60);
    let texture = new THREE.CanvasTexture(canvas);
    // texture.flipY = true;
    let result = lonlatToEarthVector3AndR(center[0],center[1],render.earthSize);
    let plan = new THREE.PlaneBufferGeometry(1,1);
    // plan.rotateX(THREE.MathUtils.degToRad(180));
    plan.rotateY(THREE.MathUtils.degToRad(180));
    let mesh = new THREE.Mesh(plan,new THREE.MeshStandardMaterial({color:0xfece61,side:0,map:texture,transparent:true,depthWrite:true,polygonOffset:true,polygonOffsetFactor:-10,alphaTest:0.1}))
    mesh.renderOrder = 9999;
    // mesh.rotation.set(0,result.lg,0);
    mesh.position.set(result.x,result.y,result.z);
    mesh.scale.set(canvas.width * 5,canvas.height * 5,1);
    mesh.lookAt(new THREE.Vector3(0,800,0));
    return mesh;
}
function creatEarthCountyName1(name,num,center,fontSize,isImage){
    if(!fontSize) fontSize = 30;
    if(name=="刚果共和国（刚果（布）") name = "刚果（布）";
    else if(name=="刚果共和国（刚果（金）") name = "刚果（金）";
    else if(name=="阿拉伯联合酋长国（阿联酋）") name = "阿联酋";
    let canvas = document.createElement("canvas");
    let ctx = canvas.getContext("2d");
    ctx.font = `bold ${fontSize}px sans-serif`;
    const textW = ctx.measureText(name).width;
    const textW1 = ctx.measureText(num).width;
    const width = Math.max(textW,textW1);
    canvas.width = width + 20;
    canvas.height = 62;
    let imageH = 0;
    if(isImage){
        imageH = 60;
        canvas.height += imageH;
        if(canvas.width<74) canvas.width = 74;
    }
    ctx.fillStyle = "#fff";
    ctx.font = `bold ${fontSize}px sans-serif`;
    ctx.shadowBlur = 2;        // 阴影模糊程度
    ctx.shadowOffsetX = 3;       // 水平方向阴影偏移量
    ctx.shadowOffsetY = 3;       // 垂直方向阴影偏移量
    ctx.shadowColor = '#000'; // 阴影颜色
    ctx.strokeStyle = '#000';
    ctx.strokeText(name,10 + (width - textW) / 2,imageH + 30);
    ctx.fillText(name,10 + (width - textW) / 2,imageH + 30);
    ctx.strokeText(num,10 + (width - textW1) / 2,imageH + 60);
    ctx.fillText(num,10 + (width - textW1) / 2,imageH + 60);
    let texture = new THREE.CanvasTexture(canvas);
    let material = new THREE.MeshStandardMaterial({color:0xfece61,side:0,map:texture,transparent:true,depthWrite:true,polygonOffset:true,polygonOffsetFactor:-10,alphaTest:0.1});
    if(isImage){
        let img = new Image();
        img.src = "img/point.png";
        img.onload = function(){
            ctx.drawImage(img,canvas.width / 2 - img.width / 2,0,img.width,img.height);
            texture.needsUpdate = true;
            material.needsUpdate = true;
        }
    }
    // texture.flipY = true;
    // let result = lonlatToEarthVector3AndR(center[0],center[1],render.earthSize);
    let plan = new THREE.PlaneBufferGeometry(1,1);
    // plan.rotateX(THREE.MathUtils.degToRad(180));
    plan.rotateY(THREE.MathUtils.degToRad(180));
    let mesh = new THREE.Mesh(plan,material)
    mesh.renderOrder = 9999;
    // mesh.rotation.set(0,result.lg,0);
    mesh.position.set(center.x,center.y,center.z);
    mesh.scale.set(canvas.width * 5,canvas.height * 5,1);
    mesh.rotation.x = THREE.MathUtils.degToRad(90);
    // mesh.lookAt(new THREE.Vector3(0,800,0));
    return mesh;
}

function lonlatToEarthVector3AndR(longitude, latitude,radius) {
    var lg = THREE.MathUtils.degToRad(90 - longitude);// - THREE.MathUtils.degToRad(110);
    var lt = THREE.MathUtils.degToRad(latitude);
    var temp = radius * Math.cos(lt);
    var x = temp * Math.sin(lg);
    var y = radius * Math.sin(lt);
    var z = temp * Math.cos(lg);
    // console.log(new THREE.Spherical( radius, lg, lt ));
    return {
        x: x,
        y: y,
        z: -z,
        lg:lg,
        lt:lt
    }
}
//切换语言
function changeLanguage(language){
    if(render.currCountrysModel.level=="earth"){
        render.language = language || "cn";
        clearCountyNames();
        showCountryMeshs(render.earthObject.currModel);
    }else if(render.currCountrysModel.level=="province"){
        $(".countryName").each(function(){
            const name_en = $(this).attr("name_en");
            const name_cn = $(this).attr("name_cn");
            if(language=="cn"){
                $(this).find("span").html(name_cn);
            }else{
                if(name_en) $(this).find("span").html(name_en);
            }
        })
    }
}
function initCss3DLabel(html){
    const details = document.createElement( 'div' );
    details.className = 'details';
    details.innerHTML = html;
    const objectCSS = new CSS3DObject( details );
    objectCSS.rotation.y = THREE.MathUtils.degToRad(180)
    objectCSS.rotation.x = THREE.MathUtils.degToRad(90)
    objectCSS.scale.set(0.01,0.01,0.01);
    return objectCSS;
    // render.scene.add( objectCSS );
}
function initJoinLine(points,num,scale){
    // const material = new THREE.LineDashedMaterial( {
    //     color: 0xcccccc,
    //     scale: 1,
    //     dashSize: 1,
    //     gapSize: 2,
    //     depthTest:false,
    //     depthWrite:false,
    //     // blending:2,
    //     transparent:true,
    //     // opacity:0.9
    // } );
    
    // const geometry = new THREE.BufferGeometry().setFromPoints( points );
    
    // return new THREE.Line( geometry, material );
    return creatPath(points,creatArrowImg(136,61,30,"rgba(0,0,0,0)","#05b4d8"),scale)
}
//创建道路
function creatPath(points, img,scale,isGeo) {
    let currvePath = new THREE.CurvePath();
    let length = points.length;
    let allDistance = 0;
    for (let i = 0; i < length - 1; i++) {
        let currve = new THREE.LineCurve3(new THREE.Vector3(points[i].x, points[i].y, points[i].z), new THREE.Vector3(points[i + 1].x, points[i + 1].y, points[i + 1].z));
        currvePath.add(currve);
        allDistance += currve.getLength();
    }
    var tubeGeometry = new THREE.TubeGeometry(currvePath, length * 48, scale * 2, 4, false);
    if(isGeo) return tubeGeometry;
    let map = new THREE.TextureLoader().load(img || "img/arrow.png");
    map.wrapS = map.wrapT = THREE.RepeatWrapping;
    // map.repeat.x = Math.max(30,Math.round(allDistance / 30));
    map.repeat.x = Math.round(allDistance / (scale * 30));
    let pathMaterial = new THREE.MeshBasicMaterial({ color: 0xffffff, map: map,transparent:true,depthTest:false });
    var tube = new THREE.Mesh(tubeGeometry, pathMaterial);
    // render.pathMaterial.push(pathMaterial)
    tube.scale.y = 0.01;
    tube.renderOrder = 9999;
    return tube;
}
function creatArrowImg(w,h,arrow_r,bgColor,arrowColor){
    if(!w) w = 136;
    if(!h) h = 61;
    let canvas = document.createElement('canvas');
    canvas.width = w;
    canvas.height = h;
    let ctx = canvas.getContext('2d');
    ctx.fillStyle = bgColor;
    ctx.fillRect(0,0,w,h);
    ctx.fillStyle = arrowColor;
    let cx = w / 2;
    ctx.fillRect(cx - arrow_r,0,arrow_r * 2,h);
    return canvas.toDataURL("image/png");
}
//json转geojson数据
function jsonToGeoJSON(){
    $.ajax({ 
        type : "GET", //提交方式 
        url : "./data/world1.json",
        async: false,
        success : function(response) {//返回数据根据结果进行相应的处理 
           if(typeof response == "string") response = JSON.parse(response);
            let features = response.features;
            // console.log(features);
            let result = {features:[],type:"FeatureCollection"};
            for(let i=0;i<features.length;i++){
                let feature = null;
                if(features[i].geometry.rings.length>1){
                    let coordinates = [];
                    for(let j=0;j<features[i].geometry.rings.length;j++){
                        let points = [];
                        for(let k=0;k<features[i].geometry.rings[j].length;k++){
                            let _coord = features[i].geometry.rings[j][k];
                            points.push([+_coord[0].toFixed(7),+_coord[1].toFixed(7)]);
                        }
                        coordinates.push([points]);
                    }
                    feature = {geometry:{type:"MultiPolygon",coordinates:coordinates},properties:{name_zh:features[i].attributes["名称"],name:features[i].attributes["Name"]}}
                }else{
                    let points = [];
                    for(let k=0;k<features[i].geometry.rings[0].length;k++){
                        let _coord = features[i].geometry.rings[0][k];
                        points.push([+_coord[0].toFixed(7),+_coord[1].toFixed(7)]);
                    }
                    feature = {geometry:{type:"Polygon",coordinates:[points]},properties:{name_zh:features[i].attributes["名称"],name:features[i].attributes["Name"]}}
                    console.log(feature,"-----------")
                }
                result.features.push(feature);
            }
            console.log(result);
            saveJSON(result);
        }
    })
}
function initWorldSFJson(){
    $.ajax({ 
        type : "GET", //提交方式 
        url : "./data/config.json",
        async: false,
        success : function(response) {//返回数据根据结果进行相应的处理 
            if(typeof response == "string") response = JSON.parse(response);
            let configs = {};
            for(let i=0;i<response.length;i++){
                configs[response[i].name] = response[i];
            }
            $.ajax({ 
                type : "GET", //提交方式 
                url : "./data/world-shen.json",
                async: false,
                success : function(response) {//返回数据根据结果进行相应的处理 
                   if(typeof response == "string") response = JSON.parse(response);
                   let features = response.features;
                   let worldSFDatas = {};
                   for(let i=0;i<features.length;i++){
                    //    console.log(JSON.stringify(features[i].attributes))
                       let country = features[i].attributes.admin; //COUNTRY;
                       let rings = features[i].geometry.rings;
                       let name = features[i].attributes.name; //NAME_1;
                       let name_zh = features[i].attributes.name_zh; //NAME_1;
                    //    if(name_zh=="阿布扎比酋长国") name_zh = "阿布扎比";
                       if(configs[name]){
                           name_zh = configs[name].name_zh;
                       }
                      if(!worldSFDatas[country]) worldSFDatas[country] = {};
                      if(!worldSFDatas[country][name_zh]) worldSFDatas[country][name_zh] = {rings:[],name_en:name};
                      for(let j=0;j<rings.length;j++){
                        worldSFDatas[country][name_zh].rings.push(rings[j]);
                      }
                   }
                   render.worldSFDatas = {};
                   for(let key in worldSFDatas){
                    if(!render.worldSFDatas[key]) render.worldSFDatas[key] = [];
                    let countrys = worldSFDatas[key];
                    let center = null;
                    for(let key1 in countrys){
                        let coords = countrys[key1].rings;
                        let name_en = countrys[key1].name_en;
                        let maxX = -Infinity,maxY = -Infinity,minX = Infinity,minY = Infinity;
                        for(let i=0;i<coords.length;i++){
                            for(let j=0;j<coords[i].length;j++){
                                maxX = Math.max(maxX,coords[i][j][0]);
                                maxY = Math.max(maxY,coords[i][j][1]);
                                minX = Math.min(minX,coords[i][j][0]);
                                minY = Math.min(minY,coords[i][j][1]);
                            }
                        }
                        center = [minX + (maxX - minX) / 2,minY + (maxY - minY) / 2];
                        render.worldSFDatas[key].push({properties:{name:key1,center:center,isGW:true,name_en:name_en},coordinates:coords});
                    }
                   }
                }
            })
        }
    })
}
function initWorld1Json(){
    $.ajax({ 
        type : "GET", //提交方式 
        url : "./data/world1.json",
        async: false,
        success : function(response) {//返回数据根据结果进行相应的处理 
           if(typeof response == "string") response = JSON.parse(response);
           let features = response.features;
        //    console.log(features,"----------")
           let world1Datas = {};
           for(let i=0;i<features.length;i++){
               let country = features[i].attributes.COUNTRY;
               let rings = features[i].geometry.rings;
              if(!world1Datas[country]) world1Datas[country] = [];
              for(let j=0;j<rings.length;j++){
                world1Datas[country].push(rings[j]);
              }
           }
           render.world1Datas = {};
           for(let key in world1Datas){
            // if(!render.world1Datas[key]) render.world1Datas[key] = [];
            let coords = world1Datas[key];
            let center = null;
            let maxX = -Infinity,maxY = -Infinity,minX = Infinity,minY = Infinity;
            // for(let i=0;i<coords.length;i++){
            //     maxX = Math.max(maxX,coords[i][0]);
            //     maxY = Math.max(maxY,coords[i][1]);
            //     minX = Math.min(minX,coords[i][0]);
            //     minY = Math.min(minY,coords[i][1]);
            // }
            for(let i=0;i<coords.length;i++){
                for(let j=0;j<coords[i].length;j++){
                    maxX = Math.max(maxX,coords[i][j][0]);
                    maxY = Math.max(maxY,coords[i][j][1]);
                    minX = Math.min(minX,coords[i][j][0]);
                    minY = Math.min(minY,coords[i][j][1]);
                }
            }
            center = [minX + (maxX - minX) / 2,minY + (maxY - minY) / 2];
            render.world1Datas[key] = {properties:{name:key,center:center,isGW:true},coordinates:world1Datas[key]};
           }
        }
    })
}
function getProjectPhotoUrls(photoIds){
    let photoIp = "http://*************:20600";
    httpAjax("/admin-api/globalManage/zjmanage/largescreen/getToken",function(res){
        console.log(res);
        let token_ = res.data;
        if(!token_) return;
        const userId = "941981453197164545"; // 固定的userId
        for(let i=0;i<photoIds.length;i++){
            const photoUrl = `${photoIp}/papi/openapi/api/file/v2/common/download/${photoIds[i]}?access_token=${token_}&userid=${userId}`;
            // console.log(photoUrl,"----------")
            let sel = $(".xmydInfo_title[img='"+photoIds[i]+"']").attr("sel");
            if(sel){
                $(".xmydInfo_title[img='"+photoIds[i]+"']").parent().parent().find(".xmydInfo_imgBg").find("img").attr("src",photoUrl);
            }
            $(".xmydInfo_title[img='"+photoIds[i]+"']").attr("img",photoUrl);
        }
    })
}