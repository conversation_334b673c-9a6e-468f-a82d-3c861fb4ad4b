<template>
  <div class="project-detail-container">
    <!-- 国外项目展示 -->
    <template v-if="isInternationalProject">
      <!-- 工程概况 -->
      <div class="section engineering-overview">
        <div class="info-section">
          <div class="info-section-bg">工程概况</div>
        </div>
        <div class="overview-content">
          <div class="overview-row">
            <div class="overview-item">
              <span class="label">项目业主方：</span>
              <span class="value">{{ processedData.项目业主方 }}</span>
            </div>
          </div>
          <div class="overview-row">
            <div class="overview-item">
              <span class="label">资金来源：</span>
              <span class="value">{{ processedData.资金来源 }}</span>
            </div>
          </div>
          <div class="overview-row">
            <div class="overview-item">
              <span class="label">建设内容：</span>
              <el-tooltip
                effect="dark"
                :content="processedData.建设内容"
                placement="top-start"
                :show-after="200"
                :hide-after="100"
                :enterable="false"
                popper-class="custom-tooltip-popper wide-tooltip"
                :disabled="!processedData.建设内容 || processedData.建设内容.length <= 15"
              >
                <span class="value text-ellipsis-multiline clickable-text">{{ processedData.建设内容 }}</span>
              </el-tooltip>
            </div>
          </div>
          <div class="overview-row">
            <div class="overview-item">
              <span class="label">合同类型：</span>
              <span class="value">{{ processedData.合同类型 }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 项目统计数据 -->
      <div class="section project-stats">
        <div class="stats-grid">
          <div class="stat-item">
            <RotatingCircle style="width: 72px;height: 72px;">
              <svg-icon name="dollar" color="#fff" style="width: 36px;height: 36px;" />
            </RotatingCircle>
            <div class="stat-content">
              <div class="stat-label">合同金额</div>
              <div class="stat-value">
                <span class="number">{{ formatContractAmount(processedData.合同额) }}</span>
                <span class="unit">万美元</span>
              </div>
            </div>
          </div>

          <div class="stat-item">
            <RotatingCircle style="width: 72px;height: 72px;">
              <svg-icon name="duration" color="#fff" style="width: 36px;height: 36px;" />
            </RotatingCircle>
            <div class="stat-content">
              <div class="stat-label">总工期</div>
              <div class="stat-value">
                <span class="number">{{ processedData.工期 }}</span>
                <span class="unit">天</span>
              </div>
            </div>
          </div>

          <div class="stat-item">
            <RotatingCircle style="width: 72px;height: 72px;">
              <svg-icon name="startWork" color="#fff" style="width: 36px;height: 36px;" />
            </RotatingCircle>
            <div class="stat-content">
              <div class="stat-label">开工日期</div>
              <div class="stat-value">
                <span class="number">{{ formatDate(processedData.开工日期) }}</span>
                <span class="unit"></span>
              </div>
            </div>
          </div>

          <div class="stat-item">
            <RotatingCircle style="width: 72px;height: 72px;">
              <svg-icon name="startWork" color="#fff" style="width: 36px;height: 36px;" />
            </RotatingCircle>
            <div class="stat-content">
              <div class="stat-label">计划竣工日期</div>
              <div class="stat-value">
                <span class="number">{{ formatDate(processedData.计划竣工日期) }}</span>
                <span class="unit"></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 项目人员 -->
      <div class="section project-personnel">
        <div class="info-section">
          <div class="info-section-bg">项目人员</div>
        </div>
        <div class="personnel-content">
          <div class="personnel-stats">
            <div class="personnel-item">
              <RotatingCircle style="width: 72px;height: 72px;">
                <svg-icon name="user-3-fill" color="#fff" style="width: 36px;height: 36px;" />
              </RotatingCircle>
              <div class="personnel-label">项目总人员</div>
              <div class="personnel-value">
                <span class="number">{{ processedData.项目总人数 }}</span>
                <span class="unit">人</span>
              </div>
            </div>
            <!-- 添加饼图展示中方与外籍比例 -->
          <div class="chart">
            <InternationalPieChart
              :key="`international-pie-${chartKey}`"
              :data="internationalPieData">
            </InternationalPieChart>
          </div>
          </div>
        </div>
      </div>

       <!-- 施工进度 -->
      <div class="section construction-progress">
        <div class="info-section">
          <div class="info-section-bg">施工进度</div>
        </div>
        <div class="progress-content">
          <div class="progress-stats">
            <div class="progress-item">
              <RotatingCircle style="width: 72px;height: 72px;">
                <svg-icon name="startWork" color="#fff" style="width: 36px;height: 36px;" />
              </RotatingCircle>
              <div class="progress-content-item">
                <div class="progress-label">施工进度</div>
                <div class="progress-value">
                  <span class="number">{{ formatProgress(processedData.施工进度) }}</span>
                  <span class="unit">%</span>
                </div>
              </div>
            </div>

            <div class="progress-item">
              <RotatingCircle style="width: 72px;height: 72px;">
                <svg-icon name="dollar" color="#fff" style="width: 36px;height: 36px;" />
              </RotatingCircle>
              <div class="progress-content-item">
                <div class="progress-label">累计完成产值</div>
                <div class="progress-value">
                  <span class="number">{{ formatContractAmount(processedData.累计完成产值) }}</span>
                  <span class="unit">万美元</span>
                </div>
              </div>
            </div>

            <div class="progress-item">
              <RotatingCircle style="width: 72px;height: 72px;">
                <svg-icon name="dollar" color="#fff" style="width: 36px;height: 36px;" />
              </RotatingCircle>
              <div class="progress-content-item">
                <div class="progress-label">回款总额</div>
                <div class="progress-value">
                  <span class="number">{{ formatContractAmount(processedData.回款总额 || processedData.工程回款总额) }}</span>
                  <span class="unit">万美元</span>
                </div>
              </div>
            </div>

            <div class="progress-item">
              <RotatingCircle style="width: 72px;height: 72px;">
                <svg-icon name="duration" color="#fff" style="width: 36px;height: 36px;" />
              </RotatingCircle>
              <div class="progress-content-item">
                <div class="progress-label">剩余工期</div>
                <div class="progress-value">
                  <span class="number">{{ processedData.剩余工期 }}</span>
                  <span class="unit">天</span>
                </div>
              </div>
            </div>
          </div>

          <div class="progress-description">
            <div class="overview-item">
              <span class="label">形象进度：</span>
              <el-tooltip
                effect="dark"
                :content="processedData.形象进度描述"
                placement="top-start"
                :show-after="200"
                :hide-after="100"
                :enterable="false"
                popper-class="custom-tooltip-popper wide-tooltip"
                :disabled="!processedData.形象进度描述 || processedData.形象进度描述.length <= 15"
              >
                <span class="value text-ellipsis-multiline clickable-text" style="white-space: pre-line">{{ processedData.形象进度描述 }}</span>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- 国内项目展示（使用原来的模板样式） -->
    <template v-else>
      <div class="domestic-content">
        <!-- 项目基本信息 -->
        <div class="section project-basic-info">
          <div class="info-section">
            <div class="info-section-bg">项目基本信息</div>
          </div>
          <div class="basic-info-content">
            <!-- 第一行：建设单位 -->
            <div class="info-row row-1">
              <div class="item">
                <div class="icon">
                  <img :src="getImg('project/icon1.png')" alt="" />
                </div>
                <div class="info">
                  <div class="title">建设单位</div>
                  <div class="value">{{ processedDomesticData.建设单位 }}</div>
                </div>
              </div>
            </div>
            
            <!-- 第二行：工程类别、合同额、工期 -->
            <div class="info-row row-3">
              <div class="item">
                <div class="icon">
                  <img :src="getImg('project/icon2.png')" alt="" />
                </div>
                <div class="info">
                  <div class="title">工程类别</div>
                  <div class="value">{{ processedDomesticData.工程类别 }}</div>
                </div>
              </div>
              <div class="item">
                <div class="icon">
                  <img :src="getImg('project/icon3.png')" alt="" />
                </div>
                <div class="info">
                  <div class="title">合同额</div>
                  <div class="value">{{ processedDomesticData.合同金额 }}</div>
                </div>
              </div>
              <div class="item">
                <div class="icon">
                  <img :src="getImg('project/icon4.png')" alt="" />
                </div>
                <div class="info">
                  <div class="title">工期</div>
                  <div class="value">{{ processedDomesticData.总工期 }}天</div>
                </div>
              </div>
            </div>
            
            <!-- 第三行：开工日期、竣工日期 -->
            <div class="info-row row-3">
              <div class="item">
                <div class="icon">
                  <img :src="getImg('project/icon5.png')" alt="" />
                </div>
                <div class="info">
                  <div class="title">开工日期</div>
                  <div class="value">{{ formatDate(processedDomesticData.开工日期) }}</div>
                </div>
              </div>
              <div class="item">
                <div class="icon">
                  <img :src="getImg('project/icon6.png')" alt="" />
                </div>
                <div class="info">
                  <div class="title">竣工日期</div>
                  <div class="value">{{ formatDate(processedDomesticData.合同竣工日期) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 项目管理 -->
        <div class="section project-management">
          <div class="info-section">
            <div class="info-section-bg">项目管理</div>
          </div>
          <div class="management-content">
            <!-- 一行：施工进度、工程回款总额、产值 -->
            <div class="info-row row-1">
              <div class="item">
                <div class="icon">
                  <img :src="getImg('project/icon1.png')" alt="" />
                </div>
                <div class="info">
                  <div class="title">产值</div>
                  <div class="value">{{ processedDomesticData.产值 }}万元</div>
                </div>
              </div>
              <div class="item">
                <div class="icon">
                  <img :src="getImg('project/icon5.png')" alt="" />
                </div>
                <div class="info">
                  <div class="title">工程回款总额</div>
                  <div class="value">{{ processedDomesticData.工程回款总额 }}万元</div>
                </div>
              </div>
              <div class="item">
                <div class="icon">
                  <img :src="getImg('project/icon4.png')" alt="" />
                </div>
                <div class="info">
                  <div class="title">施工进度</div>
                  <div class="value">{{ processedDomesticData.施工进度 * 100 }}%</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 国内项目人员 -->
        <div class="section project-personnel">
          <div class="info-section">
            <div class="info-section-bg clickable-title" @click="handlePersonnelTitleClick">项目人员</div>
          </div>
          <div class="management-content">
            <div class="info-row row-1">
              <div class="item">
                <div class="icon">
                  <img :src="getImg('project/icon1.png')" alt="" />
                </div>
                <div class="info">
                  <div class="title">劳务总人数</div>
                  <div class="value">{{ processedDomesticData.劳务总人数 }}人</div>
                </div>
              </div>
              <div class="item" style="width: 360px;">
                <div class="info" style="width: 100%; margin-left: 40px;">
                  <div class="title">人员出勤比例</div>
                  <div style="height: 150px; display: flex; align-items: center; justify-content: center;">
                    <DomesticPieChart
                      :key="`domestic-pie-${chartKey}`"
                      :data="domesticPieData"
                      style="width: 100%; height: 100%;">
                    </DomesticPieChart>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
  

</template>

<script setup>
import { nextTick, ref, watch, onMounted, onActivated, computed } from "vue";
import { useI18n } from 'vue-i18n';
import { getImg, formatNumber } from "@/utils/method";
import RotatingCircle from "@/components/RotatingCircle.vue";
import SvgIcon from '@/components/SvgIcon.vue';
import InternationalPieChart from "./InternationalPieChart.vue";
import DomesticPieChart from "./DomesticPieChart.vue";
import request from "@/utils/request";

const { t } = useI18n();

// 定义 emit
const emit = defineEmits(['show-personnel-modal']);

const props = defineProps({
  projectData: {
    type: Object,
    default: () => ({})
  },
  isInternationalProject: {
    type: Boolean,
    default: false
  },
  personData: {
    type: Object,
    default: () => ({})
  }
});

// 在script setup部分，修改项目人员数据状态初始化，添加缓存机制
const projectPersonnel = ref({
  attendance: 0,        // 出勤人数
  laborServicesNum: 0,  // 劳务总人数
  absenceFromDutyNum: 0 // 缺勤人数
});

// 添加数据缓存，按项目code存储
const projectPersonnelCache = ref({});

// 添加图表key，用于强制重新渲染图表
const chartKey = ref(0);

// 获取国内项目人员数据
const fetchProjectPersonnel = async () => {
  if (props.isInternationalProject) return;
  
  // 从项目详情中获取code字段
  const projectCode = props.projectData?.code;
  
  if (!projectCode) {
    console.error('项目详情中缺少code字段，无法获取人员数据');
    return;
  }
  
  // 检查缓存中是否有该项目的数据
  if (projectPersonnelCache.value[projectCode]) {
    console.log('使用缓存的项目人员数据:', projectCode);
    projectPersonnel.value = projectPersonnelCache.value[projectCode];
    // 强制重新渲染图表
    chartKey.value += 1;
    return;
  }
  
  try {
    console.log('正在获取项目人员数据，使用项目代码:', projectCode);
    
    // 使用POST请求调用正确的接口
    const response = await request.post("/globalManage/deviceMonitor/getNumberPerson", {
      projectCode: projectCode
    });
    
    console.log('人员数据接口响应:', response);
    
    if (response.code === 0 && response.data) {
      // 保存到当前数据和缓存
      projectPersonnel.value = response.data || {
        attendance: 0,
        laborServicesNum: 0,
        absenceFromDutyNum: 0
      };
      // 存入缓存
      projectPersonnelCache.value[projectCode] = { ...projectPersonnel.value };
      console.log('获取项目人员数据成功:', projectPersonnel.value);
    } else {
      console.error('获取项目人员数据失败:', response);
      // 如果请求失败但有缓存数据，则使用缓存数据
      if (projectPersonnelCache.value[projectCode]) {
        projectPersonnel.value = projectPersonnelCache.value[projectCode];
      } else {
        // 设置默认值以防接口失败
        projectPersonnel.value = {
          attendance: 0,
          laborServicesNum: 0,
          absenceFromDutyNum: 0
        };
      }
    }
    // 强制重新渲染图表
    chartKey.value += 1;
  } catch (error) {
    console.error('获取项目人员数据异常:', error);
    // 如果请求出错但有缓存数据，则使用缓存数据
    if (projectPersonnelCache.value[projectCode]) {
      projectPersonnel.value = projectPersonnelCache.value[projectCode];
    } else {
      // 设置默认值以防接口出错
      projectPersonnel.value = {
        attendance: 0,
        laborServicesNum: 0,
        absenceFromDutyNum: 0
      };
    }
    // 强制重新渲染图表
    chartKey.value += 1;
  }
};



// 格式化合同金额
const formatContractAmount = (amount) => {
  if (!amount) return '2,690.8';
  if (typeof amount === 'string') {
    return amount.replace(/,/g, '');
  }
  return formatNumber(amount);
};

// 格式化日期
const formatDate = (date) => {
  if (!date) return '';
  if (typeof date === 'string' && date.includes(' ')) {
    return date.split(' ')[0].replace(/-/g, '/');
  }
  return date;
};

// 格式化进度
const formatProgress = (progress) => {
  if (!progress) return '89.9';
  if (typeof progress === 'string') {
    return progress.replace('%', '');
  }
  return progress;
};



// 新增：处理接口数据的函数（用于国外项目和国内项目）
const processProjectData = (data) => {
  if (!data) return {};
  console.log(data);
  // 根据接口数据示例进行映射
  return {
    项目名称: data.项目名称 || data.项目简称 || '',
    项目业主方: data.项目业主方 || data.建设单位 || '',
    资金来源: data.资金来源 || '',
    建设内容: data.建设内容 || data.工程类别 || '',
    合同类型: data.合同类型 || '',
    合同额: data.合同额 || data.合同金额 || '0',
    工期: data.工期 || data.总工期 || '0',
    开工日期: data.开工日期 || '',
    预付款: data.预付款 || '0',
    合同签订日期: data.合同签订日期 || '',
    计划竣工日期: data.计划竣工日期 || data.合同竣工日期 || '',
    施工进度: data.施工进度 || '0',
    累计完成产值: data.累计完成产值 || data.产值 || '0',
    回款总额: data.回款总额 || data.工程回款总额 || data.汇款总额 || '0',
    剩余工期: data.剩余工期 || '0',
    形象进度描述: data.形象进度描述 || '',
    项目总人数: data.项目总人数 || '0',
    中方: data.中方 || '0',
    外籍: data.外籍 || '0'
  };
};

// 计算属性：处理后的项目数据
const processedData = computed(() => {
  return processProjectData(props.projectData);
});

// 计算属性：处理后的国内项目数据
const processedDomesticData = computed(() => {
  if (!processedData.value) return {};
  
  // 处理合同金额，如果为空或0，显示"0万元"，否则加上"万元"
  const 合同金额 = processedData.value.合同额;
  const 格式化合同金额 = (!合同金额 || 合同金额 === '' || 合同金额 === '0') ? '0万元' : `${合同金额}万元`;
  
  // 处理施工进度 - 检查是否已经包含%符号
  let 施工进度值 = processedData.value.施工进度 || '0';
  if (typeof 施工进度值 === 'string' && 施工进度值.includes('%')) {
    // 如果已经包含%，去掉%符号，只保留数字
    施工进度值 = 施工进度值.replace('%', '');
  }
  
  // 获取人员数据，优先使用自己请求的projectPersonnel数据，其次使用props中的personData
  const attendance = projectPersonnel.value.attendance !== undefined ? projectPersonnel.value.attendance : 
                    (props.personData.attendance !== undefined ? props.personData.attendance : 0);
  
  const laborServicesNum = projectPersonnel.value.laborServicesNum !== undefined ? projectPersonnel.value.laborServicesNum : 
                          (props.personData.laborServicesNum !== undefined ? props.personData.laborServicesNum : 0);
  
  const absenceFromDutyNum = projectPersonnel.value.absenceFromDutyNum !== undefined ? projectPersonnel.value.absenceFromDutyNum : 
                            (props.personData.absenceFromDutyNum !== undefined ? props.personData.absenceFromDutyNum : 0);
  
  return {
    建设单位: processedData.value.项目业主方 || '',
    工程类别: processedData.value.建设内容 || '',
    合同金额: 格式化合同金额,
    总工期: processedData.value.工期 || '0',
    开工日期: processedData.value.开工日期 || '',
    合同竣工日期: processedData.value.计划竣工日期 || '',
    施工进度: 施工进度值, // 返回纯数字，模板中再加%
    工程回款总额: processedData.value.回款总额 || '0',
    产值: processedData.value.累计完成产值 || '0',
    项目总人数: processedData.value.项目总人数 || '0',
    出勤人数: attendance,
    劳务总人数: laborServicesNum,
    缺勤人数: absenceFromDutyNum
  };
});

// 国际项目人员饼图数据
const internationalPieData = computed(() => {
  if (!processedData.value) return [];
  
  const pieDataArray = [];
  
  // 添加中方人员数据
  if (processedData.value.中方) {
    pieDataArray.push({
      name: '中方人员',
      value: Number(processedData.value.中方) || 0,
      itemStyle: { color: "rgba(0, 216, 255, 1)" },
    });
  }
  
  // 添加外籍人员数据
  if (processedData.value.外籍) {
    pieDataArray.push({
      name: '外籍人员',
      value: Number(processedData.value.外籍) || 0,
      itemStyle: { color: "rgba(0, 255, 187, 1)" },
    });
  }
  
  // 如果没有有效数据，添加一个默认项避免图表为空
  if (pieDataArray.length === 0) {
    pieDataArray.push({
      name: '暂无数据',
      value: 1,
      itemStyle: { color: "rgba(128, 128, 128, 0.5)" },
    });
  }
  
  return pieDataArray;
});

// 国内项目人员饼图数据
const domesticPieData = computed(() => {
  if (!processedDomesticData.value) return [];
  
  // 获取出勤人数和缺勤人数
  const attendance = Number(processedDomesticData.value.出勤人数) || 0;
  const absenceFromDuty = Number(processedDomesticData.value.缺勤人数) || 0;
  
  const pieDataArray = [];
  
  // 如果两者都为0，添加一个默认项避免图表为空
  if (attendance === 0 && absenceFromDuty === 0) {
    pieDataArray.push({
      name: '出勤人员',
      value: 0,
      itemStyle: { color: "rgba(0, 216, 255, 0.5)" },
    });
    pieDataArray.push({
      name: '缺勤人员',
      value: 0,
      itemStyle: { color: "rgba(0, 255, 187, 0.5)" },
    });
  } else {
    // 添加出勤人员数据
    pieDataArray.push({
      name: '出勤人员',
      value: attendance,
      itemStyle: { color: "rgba(0, 216, 255, 1)" },
    });
    
    // 添加缺勤人员数据
    pieDataArray.push({
      name: '缺勤人员',
      value: absenceFromDuty,
      itemStyle: { color: "rgba(0, 255, 187, 1)" },
    });
  }
  
  console.log('生成饼图数据:', pieDataArray);
  return pieDataArray;
});

// 修复错误：合并重复的watch并去除oldData引用

// 删除重复的watch和onActivated，保留一个优化后的watch
watch(() => props.projectData, (newData) => {
  console.log('项目数据更新:', newData);
  
  // 检查是否为国内项目且项目数据中有code字段
  if (!props.isInternationalProject && newData && newData.code) {
    console.log('检测到国内项目，获取人员数据:', newData.code);
    fetchProjectPersonnel();
  }
}, { deep: true });

// 添加onMounted钩子确保初始数据加载
onMounted(() => {
  console.log('项目数据:', props.projectData);
  console.log('是否为国际项目:', props.isInternationalProject);
  console.log('人员数据:', props.personData);
  
  // 初始加载数据
  if (!props.isInternationalProject && props.projectData && props.projectData.code) {
    console.log('组件挂载，初始加载国内项目人员数据');
    fetchProjectPersonnel();
  }
});



// 组件激活时重新加载数据
onActivated(() => {
  if (!props.isInternationalProject && props.projectData && props.projectData.code) {
    console.log('组件激活，获取国内项目人员数据');
    fetchProjectPersonnel();
  }
});

// 处理项目人员标题点击事件
const handlePersonnelTitleClick = () => {
  emit('show-personnel-modal');
};
</script>
<style>
.el-popper {
  max-width: 600px;
}
</style>
<style lang="scss" scoped>
.project-detail-container {
  width: 100%;
  height: 100%;
  padding: 20px 0 20px 20px;
  display: flex;
  flex-direction: column;
  gap: 15.6px;
  position: relative;
  background: rgba(10, 15, 26, 0.8);
  border-radius: 8px;
  // border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  z-index: 1;
  overflow-y: auto;
  
  // 隐藏滚动条
  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
  }

  .section {
    position: relative;
    background: transparent !important;
    // background: rgba(5, 15, 35, 0.8);
    // border: 1px solid rgba(77, 151, 255, 0.3);
    // border-radius: 8px;
    // padding: 20px;
    // margin-bottom: 16px;
  }

  /* 信息部分标题样式 - 参考item2.vue */
  .info-section {
    width: 624px;
    height: 54.6px;
    margin: 0 0 31.2px 0;

    .info-section-bg {
      width: 624px;
      height: 62.4px;
      background: url("@/assets/images/map/projectInfo.png") no-repeat;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      display: flex;
      align-items: center;
      padding-left: 62.4px;
      font-family: PangMenZhengDao, PangMenZhengDao;
      font-size: 31.2px;
      color: #FFFFFF;
      line-height: 35.88px;
      letter-spacing: 3.12px;
      text-align: justified;
      font-style: normal;
      text-transform: none;
    }
  }

  // 工程概况样式
  .engineering-overview {
    .overview-content {
      background: rgba(61,126,240,0.1);
      padding: 12px 24px 4.8px 24px;
      .overview-row {
        margin-bottom: 14.4px;

        .overview-item {
          display: flex;
          align-items: flex-start;

          &.full-width {
            flex-direction: column;
            
            .label {
              margin-bottom: 9.6px;
            }
          }

          .label {
            font-size: 25px;
            color: #A3D2EB;
            min-width: 187.2px;
            white-space: nowrap;
          }

          .value {
            font-size: 25px;
            color: #FFFFFF;
            line-height: 2.34;
            flex: 1;

                          &.text-ellipsis-multiline {
                display: -webkit-box;
                -webkit-line-clamp: 7;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
                cursor: help;
                
                &:hover {
                  position: relative;
                  z-index: 1000;
                }
            }
          }
        }
      }
    }

    // 国内项目简化样式
    &.domestic {
      .overview-content.domestic {
        background: linear-gradient(135deg, rgba(61,126,240,0.15) 0%, rgba(30,100,220,0.1) 100%);
        border: 1px solid rgba(77, 151, 255, 0.2);
        border-radius: 8px;
        padding: 24px;
        
        .overview-row {
          margin-bottom: 19.2px;
          
          &:last-child {
            margin-bottom: 0;
          }

          .overview-item {
            .label {
              font-size: 21.6px;
              font-weight: 500;
              min-width: 120px;
              background: linear-gradient(90deg, #A3D2EB 0%, #7FB8E5 100%);
              -webkit-background-clip: text;
              background-clip: text;
              -webkit-text-fill-color: transparent;
            }

            .value {
              font-size: 21.6px;
              font-weight: 600;
              color: #FFFFFF;
              text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
            }
          }
        }
      }
    }
  }

  // 项目统计数据样式
  .project-stats {
            .stats-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 25px;

      .stat-item {
        display: flex;
        align-items: center;
        gap: 25px;

        .stat-content {
          flex: 1;
          display: flex;
          flex-direction: column;

          .stat-label {
            font-size: 25px;
            color: #A3D2EB;
            margin-bottom: 12.48px;
            line-height: 1.872;
          }

          .stat-value {
            display: flex;
            align-items: baseline;
            line-height: 1.3;

            .number {
              font-size: 37.44px;
              font-family: TCloudNumber;
  background-image: linear-gradient(to bottom,
      #9AC4FF 0%,
      #FFFFFF 50%,
      #9AC4FF 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  font-weight: bold;
}

            .unit {
              font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
              font-weight: normal;
              font-size: 21.84px;
              color: rgba(219, 233, 255, 0.8);
              line-height: 31.2px;
              text-align: right;
              font-style: normal;
              text-transform: none;
            }
          }
        }
      }
    }
  }

  // 施工进度样式
  .construction-progress {
    .progress-content {
              .progress-stats {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 25px;
        margin-bottom: 31.2px;

        .progress-item {
          display: flex;
          align-items: center;
          gap: 25px;

          .progress-content-item {
            flex: 1;
            display: flex;
            flex-direction: column;

            .progress-label {
              font-size: 25px;
              color: #A3D2EB;
              margin-bottom: 12.48px;
              line-height: 1.872;
            }

            .progress-value {
              display: flex;
              align-items: baseline;
              line-height: 1.3;

              .number {
                font-size: 37.44px;
              font-family: TCloudNumber;
  background-image: linear-gradient(to bottom,
      #9AC4FF 0%,
      #FFFFFF 50%,
      #9AC4FF 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  font-weight: bold;
              }

              .unit {
                font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
                font-weight: normal;
                font-size: 21.84px;
                color: rgba(219, 233, 255, 0.8);
                line-height: 31.2px;
                text-align: right;
                font-style: normal;
                text-transform: none;
              }
            }
          }
        }
      }

        .progress-description {
          background: rgba(61,126,240,0.10);
          padding: 15.6px 31.2px 15.6px 31.2px;

          .overview-item {
            display: flex;
            align-items: flex-start;

            .label {
              font-size: 25px;
              color: #A3D2EB;
              min-width: 187.2px;
              white-space: nowrap;
            }

            .value {
              font-size: 25px;
              color: #FFFFFF;
              line-height: 2.34;
              flex: 1;
              white-space: pre-wrap;
              word-break: break-word;
              overflow-wrap: break-word;

              &.text-ellipsis-multiline {
                display: -webkit-box;
                -webkit-line-clamp: 4;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: normal;
                cursor: help;
                
                &:hover {
                  position: relative;
                  z-index: 1000;
                }
              }
            }
          }
        }
    }
  }

  // 项目人员样式
  .project-personnel {
    .personnel-content {
      .personnel-stats {
        display: flex;
        align-items: center;
        gap: 62.4px;

        .personnel-item {
          display: flex;
          align-items: center;
          gap: 25px;

          .personnel-label {
            font-size: 25px;
            color: #A3D2EB;
          }

          .personnel-value {
            display: flex;
            align-items: baseline;

            .number {
              font-size: 37.44px;
              font-family: TCloudNumber;
              background-image: linear-gradient(to bottom,
                  #9AC4FF 0%,
                  #FFFFFF 50%,
                  #9AC4FF 100%);
              -webkit-background-clip: text;
              background-clip: text;
              -webkit-text-fill-color: transparent;
              color: transparent;
              margin-right: 6.24px;
            }

            .unit {
              font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
              font-weight: normal;
              font-size: 21.84px;
              color: rgba(219, 233, 255, 0.8);
              line-height: 31.2px;
              text-align: right;
              font-style: normal;
              text-transform: none;
            }
          }
        }

        /* 饼图样式 */
        .chart {
          width: 360px;
          height: 160px;
          flex-shrink: 0;
        }
      }

      .personnel-breakdown {
        .breakdown-row {
          display: flex;
          gap: 48px;

          .breakdown-item {
            display: flex;
            align-items: center;

            .breakdown-label {
              font-size: 19.2px;
              color: #A3D2EB;
              margin-right: 19.2px;
              padding: 0 12px;
              background: linear-gradient( 180deg, rgba(0,132,255,0) 0%, #0084FF 50%, rgba(0,132,255,0) 100%);
            }

            .breakdown-value {
              display: flex;
              align-items: baseline;

              .number {
                font-size: 24px;
                font-family: TCloudNumber;
                background-image: linear-gradient(to bottom,
                    #9AC4FF 0%,
                    #FFFFFF 50%,
                    #9AC4FF 100%);
                -webkit-background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;
                color: transparent;
                margin-right: 4.8px;
              }

              .unit {
                font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
                font-weight: normal;
                font-size: 14.4px;
                color: rgba(219, 233, 255, 0.8);
                line-height: 24px;
                text-align: right;
                font-style: normal;
                text-transform: none;
              }
            }
          }
                }
      }
    }
  }

  /* 国内项目样式 */
  .domestic-content {
    .section {
      margin-bottom: 70px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    .basic-info-content,
    .management-content {
      padding: 35px 0;
      
      .info-row {
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;
        flex-wrap: nowrap;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .item {
          display: flex;
          align-items: center;
          //margin-right: 60px;
          
          &:last-child {
            margin-right: 0;
          }
          
          .icon {
            width: 180px;
            height: 160px;
            margin-right: 35px;
            flex-shrink: 0;
            
            img {
              width: 100%;
              height: 100%;
              object-fit: contain;
            }
          }
          
          .info {
            flex: 1;
            min-width: 280px;
            
            .title {
              font-size: 26px;
              color: #A3D2EB;
              margin-bottom: 8px;
              line-height: 1.2;
              font-weight: 400;
            }
            
            .value {
              font-size: 34px;
              color: #FFFFFF;
              font-weight: 600;
              line-height: 1.2;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
    
    .basic-info-content {
      .info-row {
        /* 第一行：建设单位（1个项目，左对齐） */
        &.row-1 {
          .item {
    width: 100%; /* 占满整个容器宽度 */
    max-width: 1200px; /* 设置最大宽度 */
  }
        }
        
        /* 第二行：工程类别、合同额、工期（3个项目，左对齐） */
        &.row-2 {
          .item {
            width: 520px;
          }
        }
        
        /* 第三行：开工日期、竣工日期（2个项目，左对齐） */
        &.row-3 {
          .item {
            width: 520px;
          }
        }
      }
    }
    
    .management-content {
      .info-row {
        /* 项目管理：一行3个项目左对齐 */
        &.row-1 {
          .item {
            width: 520px;
          }
        }
      }
    }
  }

  /* 可点击文本样式 */
  .clickable-text {
    pointer-events: auto !important;
    cursor: help;
    position: relative;
    z-index: 10;
    
    &:hover {
      color: #A3D2EB !important;
      transition: color 0.2s ease;
    }
  }

  /* 可点击标题样式 */
  .clickable-title {
    cursor: pointer !important;
    transition: all 0.3s ease;
    
    &:hover {
      color: #00D8FF !important;
      text-shadow: 0 0 10px rgba(0, 216, 255, 0.6) !important;
      transform: scale(1.02);
    }
    
    &:active {
      transform: scale(0.98);
    }
  }

  /* Element UI Tooltip 自定义样式 */
  :deep(.custom-tooltip-popper) {
    max-width: 540px !important;
    min-width: 336px !important;
    max-height: 336px !important;
    padding: 19.2px 24px !important;
    background: linear-gradient(135deg, rgba(5, 15, 35, 0.96) 0%, rgba(15, 25, 45, 0.96) 100%) !important;
    border: 1px solid rgba(77, 151, 255, 0.5) !important;
    border-radius: 12px !important;
    color: #FFFFFF !important;
    font-size: 16.8px !important;
    line-height: 1.92 !important;
    word-wrap: break-word !important;
    word-break: break-word !important;
    white-space: pre-wrap !important;
    overflow-y: auto !important;
    box-shadow: 
      0 12px 48px rgba(0, 0, 0, 0.5),
      0 0 36px rgba(77, 151, 255, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(14.4px) !important;
    z-index: 10000 !important;

    /* 长文本内容的特殊宽度 */
    &.wide-tooltip {
      width: 600px !important;
      max-width: 780px !important;
      min-width: 480px !important;
    }
    
    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.08);
      border-radius: 3.6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(77, 151, 255, 0.6);
      border-radius: 3.6px;
      
      &:hover {
        background: rgba(77, 151, 255, 0.8);
      }
    }
  }

  /* Element UI Tooltip 箭头样式 */
  :deep(.custom-tooltip-popper[data-popper-placement^="top"] .el-popper__arrow::before) {
    background: linear-gradient(135deg, rgba(5, 15, 35, 0.96) 0%, rgba(15, 25, 45, 0.96) 100%) !important;
    border: 1px solid rgba(77, 151, 255, 0.5) !important;
  }
  
  :deep(.custom-tooltip-popper .el-popper__arrow) {
    &::before {
      background: linear-gradient(135deg, rgba(5, 15, 35, 0.96) 0%, rgba(15, 25, 45, 0.96) 100%) !important;
      border: 1px solid rgba(77, 151, 255, 0.5) !important;
    }
  }
  

}
</style>
