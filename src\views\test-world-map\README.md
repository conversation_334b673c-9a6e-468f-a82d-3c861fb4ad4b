# 世界地图信息测试页面

## 页面说明
这是一个临时测试页面，用于测试 `/globalManage/zjmanage/largescreen/getWorldMapInfo` 接口。

## 功能特性
- 动态输入国家名称
- 实时调用API接口
- 显示完整的API响应结果
- 错误处理和状态显示
- 响应式设计，支持移动端

## 使用方法
1. 在输入框中输入国家名称（例如：中国、美国、日本等）
2. 点击"查询数据"按钮或按回车键
3. 查看API响应结果

## 访问地址
开发环境：http://localhost:5173/#/test-world-map

## 接口信息
- **接口路径**: `/globalManage/zjmanage/largescreen/getWorldMapInfo`
- **请求方法**: GET
- **参数**: country (国家名称)
- **示例**: `/globalManage/zjmanage/largescreen/getWorldMapInfo?country=中国`

## 技术实现
- 使用 Vue 3 Composition API
- 集成 Element Plus UI 组件
- 使用项目统一的 request 工具进行API调用
- 包含完整的错误处理和加载状态

## 注意事项
- 这是一个临时测试页面，后续会被删除
- 页面包含完整的请求日志，便于调试
- 支持中文国家名称输入

## 删除说明
当测试完成后，需要删除以下文件：
1. `src/views/test-world-map/index.vue`
2. `src/views/test-world-map/README.md`
3. 在 `src/router/index.js` 中删除对应的路由配置
