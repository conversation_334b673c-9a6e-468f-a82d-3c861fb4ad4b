<template>
  <div class="test-world-map-container" :style="style">
    <div class="header">
      <h1>世界地图信息测试页面</h1>
      <p>测试接口：/globalManage/zjmanage/largescreen/getWorldMapInfo</p>
    </div>
    
    <div class="input-section">
      <div class="input-group">
        <label for="country-input">输入国家名称：</label>
        <el-input
          id="country-input"
          v-model="countryName"
          placeholder="请输入国家名称，例如：中国、美国、日本等"
          style="width: 300px; margin-right: 10px;"
          @keyup.enter="fetchWorldMapInfo"
        />
        <el-button type="primary" @click="fetchWorldMapInfo" :loading="loading">
          查询数据
        </el-button>
        <el-button @click="clearData">清空结果</el-button>
      </div>
    </div>

    <div class="result-section">
      <div class="status-info">
        <p><strong>请求状态：</strong>
          <span :class="statusClass">{{ requestStatus }}</span>
        </p>
        <p v-if="lastRequestTime"><strong>最后请求时间：</strong>{{ lastRequestTime }}</p>
        <p v-if="requestUrl"><strong>请求URL：</strong>{{ requestUrl }}</p>
      </div>

      <div v-if="apiResponse" class="response-section">
        <h3>API响应结果：</h3>
        <div class="response-container">
          <pre>{{ JSON.stringify(apiResponse, null, 2) }}</pre>
        </div>
      </div>

      <div v-if="errorMessage" class="error-section">
        <h3>错误信息：</h3>
        <div class="error-container">
          <p>{{ errorMessage }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import request from '@/utils/request'
import { ElMessage } from 'element-plus'

// 响应式数据
const countryName = ref('')
const loading = ref(false)
const apiResponse = ref(null)
const errorMessage = ref('')
const requestStatus = ref('未发起请求')
const lastRequestTime = ref('')
const requestUrl = ref('')

// 计算属性
const statusClass = computed(() => {
  switch (requestStatus.value) {
    case '请求成功':
      return 'status-success'
    case '请求失败':
      return 'status-error'
    case '请求中...':
      return 'status-loading'
    default:
      return 'status-default'
  }
})

// 获取世界地图信息
const fetchWorldMapInfo = async () => {
  if (!countryName.value.trim()) {
    ElMessage.warning('请输入国家名称')
    return
  }

  loading.value = true
  requestStatus.value = '请求中...'
  apiResponse.value = null
  errorMessage.value = ''
  lastRequestTime.value = new Date().toLocaleString()
  requestUrl.value = `/globalManage/zjmanage/largescreen/getWorldMapInfo?country=${encodeURIComponent(countryName.value)}`

  try {
    console.log('发起API请求，国家名称:', countryName.value)
    
    const response = await request.get('/globalManage/zjmanage/largescreen/getWorldMapInfo', {
      params: {
        country: countryName.value.trim()
      }
    })

    console.log('API响应:', response)
    
    apiResponse.value = response
    requestStatus.value = '请求成功'
    
    if (response.code === 0) {
      ElMessage.success(`成功获取 ${countryName.value} 的地图信息`)
    } else {
      ElMessage.warning(`请求完成，但返回状态码: ${response.code}`)
    }
    
  } catch (error) {
    console.error('API请求失败:', error)
    errorMessage.value = error.message || '请求失败'
    requestStatus.value = '请求失败'
    ElMessage.error(`获取 ${countryName.value} 的地图信息失败`)
  } finally {
    loading.value = false
  }
}

// 清空数据
const clearData = () => {
  countryName.value = ''
  apiResponse.value = null
  errorMessage.value = ''
  requestStatus.value = '未发起请求'
  lastRequestTime.value = ''
  requestUrl.value = ''
}

// 页面加载时的提示
console.log('世界地图信息测试页面已加载')
</script>

<style scoped>
.test-world-map-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: 'Arial', sans-serif;
  pointer-events: auto; /* 恢复鼠标交互功能 */
  position: relative;
  z-index: 999; /* 确保在最上层 */
}

.header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 10px;
}

.header h1 {
  margin: 0 0 10px 0;
  font-size: 24px;
}

.header p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

.input-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  pointer-events: auto; /* 确保输入区域可交互 */
}

.input-group {
  display: flex;
  align-items: center;
  gap: 10px;
  pointer-events: auto; /* 确保输入组可交互 */
}

.input-group * {
  pointer-events: auto; /* 确保所有子元素可交互 */
}

.input-group label {
  font-weight: bold;
  color: #495057;
  white-space: nowrap;
}

.result-section {
  margin-top: 20px;
}

.status-info {
  padding: 15px;
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  margin-bottom: 20px;
}

.status-info p {
  margin: 5px 0;
  font-size: 14px;
}

.status-success {
  color: #28a745;
  font-weight: bold;
}

.status-error {
  color: #dc3545;
  font-weight: bold;
}

.status-loading {
  color: #007bff;
  font-weight: bold;
}

.status-default {
  color: #6c757d;
}

.response-section, .error-section {
  margin-top: 20px;
}

.response-section h3, .error-section h3 {
  margin-bottom: 10px;
  color: #495057;
}

.response-container {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 15px;
  max-height: 500px;
  overflow-y: auto;
}

.response-container pre {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.error-container {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 6px;
  padding: 15px;
  color: #721c24;
}

.error-container p {
  margin: 0;
  font-weight: bold;
}

/* Element Plus 组件交互修复 */
:deep(.el-input) {
  pointer-events: auto !important;
}

:deep(.el-input__wrapper) {
  pointer-events: auto !important;
}

:deep(.el-input__inner) {
  pointer-events: auto !important;
}

:deep(.el-button) {
  pointer-events: auto !important;
}

:deep(.el-button:hover) {
  pointer-events: auto !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .input-group {
    flex-direction: column;
    align-items: stretch;
  }

  .input-group label {
    margin-bottom: 5px;
  }

  .el-input {
    width: 100% !important;
    margin-right: 0 !important;
    margin-bottom: 10px;
  }
}
</style>
